# Comprehensive Regression Testing for Script-Based State Building

## Overview

This directory contains comprehensive regression test suites for the GretahAI ScriptWeaver script-based state building functionality. These tests ensure reliability, prevent regressions, and validate performance across all critical areas of the system.

## Test Suites

### 1. End-to-End Regression Tests (`test_end_to_end_regression.py`)
**Purpose**: Complete workflow validation from step table input through script generation, execution, and interactive element selection.

**Coverage**:
- Complete workflow integration testing
- ScriptBasedStateBuilder and interactive selector integration
- Fallback mechanism validation (script-based → manual replay → base URL)
- State persistence and browser session management
- Error propagation and user feedback
- Progress callback functionality
- Test case ID propagation

**Critical**: ✅ Yes

### 2. Cross-Browser Compatibility Tests (`test_cross_browser_compatibility.py`)
**Purpose**: Ensure consistent behavior across different browsers and configurations.

**Coverage**:
- Chrome, Firefox, Edge compatibility testing
- Headless vs. visible browser modes
- WebDriver version compatibility
- Browser-specific behaviors and workarounds
- Driver configuration variations
- Script generation consistency across browsers

**Critical**: ⚠️ No (environment-dependent)

### 3. Performance Regression Tests (`test_performance_regression.py`)
**Purpose**: Validate performance characteristics and prevent performance regressions.

**Coverage**:
- Script execution time benchmarks (1-step, 5-step, 10-step scenarios)
- Performance comparison against manual step replay baseline
- Memory usage and resource cleanup validation
- Timeout handling and execution limits
- Concurrent execution performance
- Script generation performance

**Critical**: ✅ Yes

### 4. Error Scenario Regression Tests (`test_error_scenario_regression.py`)
**Purpose**: Ensure robust error handling and graceful degradation.

**Coverage**:
- Malformed step tables and invalid locator strategies
- Network failures and page load timeouts
- Script generation and execution errors
- Error reporting and user feedback mechanisms
- Fallback mechanism validation
- Resource cleanup on errors
- Concurrent error handling

**Critical**: ✅ Yes

### 5. Integration Regression Tests (`test_integration_regression.py`)
**Purpose**: Validate seamless integration across all GretahAI ScriptWeaver components.

**Coverage**:
- Stage 4 UI integration with script-based state building toggle
- StateManager integration and session state persistence
- Progress callback functionality and user feedback
- Test case ID propagation and script naming
- Workflow integration across all stages
- Configuration integration

**Critical**: ✅ Yes

### 6. Data Validation Regression Tests (`test_data_validation_regression.py`)
**Purpose**: Ensure robust handling of various data formats and edge cases.

**Coverage**:
- Step table format variations and edge cases
- Test data parameter substitution validation
- Empty, null, and malformed test data handling
- Script cleanup and temporary file management
- Data type validation and conversion
- Large data set handling

**Critical**: ✅ Yes

## Running Tests

### Quick Start

```bash
# Run all regression tests
python tests/regression/run_regression_tests.py

# Run with verbose output
python tests/regression/run_regression_tests.py --verbose

# Run in parallel (faster execution)
python tests/regression/run_regression_tests.py --parallel

# Specify custom output directory
python tests/regression/run_regression_tests.py --output-dir ./custom_results
```

### Individual Test Suites

```bash
# Run specific test suite
pytest tests/regression/test_end_to_end_regression.py -v

# Run with timeout
pytest tests/regression/test_performance_regression.py -v --timeout=300

# Run with detailed output
pytest tests/regression/test_error_scenario_regression.py -v --tb=long
```

### Environment Setup

```bash
# Install required dependencies
pip install pytest pytest-timeout pytest-json-report psutil

# Set environment variables for extended testing
export TEST_FIREFOX=1  # Enable Firefox testing
export TEST_EDGE=1     # Enable Edge testing
export SCRIPTWEAVER_DEBUG=true  # Enable debug logging
```

## Test Results and Reporting

### Output Files

The test runner generates comprehensive reports in the specified output directory:

- `regression_test_summary.json` - Machine-readable test results
- `regression_test_report.txt` - Human-readable text report
- `regression_test_report.html` - HTML report with styling
- `*_report.json` - Individual test suite JSON reports

### Report Contents

**Summary Information**:
- Overall test status (SUCCESS/FAILURE/CRITICAL_FAILURE)
- Total execution time
- Test counts (total, passed, failed, skipped)
- Critical failure identification

**Detailed Results**:
- Per-suite execution results
- Performance metrics
- Error details and diagnostics
- Execution timing information

### Success Criteria

**Overall Success**: All critical test suites must pass
**Critical Test Suites**:
- End-to-End Regression ✅
- Performance Regression ✅
- Error Scenario Regression ✅
- Integration Regression ✅
- Data Validation Regression ✅

**Non-Critical Test Suites**:
- Cross-Browser Compatibility (environment-dependent)

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Script-Based State Building Regression Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  regression-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-timeout pytest-json-report psutil
    
    - name: Run regression tests
      run: |
        cd GretahAI_ScriptWeaver
        python tests/regression/run_regression_tests.py --parallel
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: regression-test-results
        path: GretahAI_ScriptWeaver/tests/regression/results/
```

### Jenkins Pipeline Example

```groovy
pipeline {
    agent any
    
    stages {
        stage('Setup') {
            steps {
                sh 'pip install -r requirements.txt'
                sh 'pip install pytest pytest-timeout pytest-json-report psutil'
            }
        }
        
        stage('Regression Tests') {
            steps {
                dir('GretahAI_ScriptWeaver') {
                    sh 'python tests/regression/run_regression_tests.py --parallel'
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'GretahAI_ScriptWeaver/tests/regression/results/**/*'
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'GretahAI_ScriptWeaver/tests/regression/results',
                        reportFiles: 'regression_test_report.html',
                        reportName: 'Regression Test Report'
                    ])
                }
            }
        }
    }
}
```

## Performance Benchmarks

### Expected Performance Characteristics

| Scenario | Expected Time | Memory Usage | Success Rate |
|----------|---------------|--------------|--------------|
| 1-step script execution | < 30 seconds | < 100 MB | > 95% |
| 5-step script execution | < 45 seconds | < 150 MB | > 90% |
| 10-step script execution | < 60 seconds | < 200 MB | > 85% |
| Script generation (20 steps) | < 5 seconds | < 20 MB | > 99% |
| Resource cleanup | < 5 seconds | Minimal growth | > 99% |

### Performance Regression Detection

The test suite automatically detects performance regressions by:
- Comparing execution times against established baselines
- Monitoring memory usage patterns
- Tracking success rate trends
- Alerting on significant performance degradation

## Troubleshooting

### Common Issues

**1. Browser Driver Issues**
```bash
# Update Chrome driver
pip install --upgrade chromedriver-autoinstaller

# Check browser versions
google-chrome --version
firefox --version
```

**2. Timeout Issues**
```bash
# Increase timeout for slow environments
pytest tests/regression/ --timeout=600
```

**3. Memory Issues**
```bash
# Monitor memory usage
python -c "import psutil; print(f'Available memory: {psutil.virtual_memory().available / 1024**3:.2f} GB')"
```

**4. Network Issues**
```bash
# Test network connectivity
curl -I https://httpbin.org/forms/post
```

### Debug Mode

Enable comprehensive debugging:
```bash
export SCRIPTWEAVER_DEBUG=true
export PYTEST_VERBOSE=true
python tests/regression/run_regression_tests.py --verbose
```

## Maintenance

### Regular Tasks

**Weekly**:
- Review test execution results
- Update performance baselines if needed
- Check for new browser versions

**Monthly**:
- Review and update test scenarios
- Analyze performance trends
- Update documentation

**Quarterly**:
- Comprehensive test suite review
- Performance benchmark updates
- CI/CD pipeline optimization

### Adding New Tests

1. **Identify Test Category**: Determine which test suite the new test belongs to
2. **Follow Naming Convention**: Use descriptive test names with `test_` prefix
3. **Include Documentation**: Add docstrings explaining test purpose and coverage
4. **Update This README**: Document new test scenarios and expected outcomes
5. **Validate Integration**: Ensure new tests work with the test runner

## Contact and Support

For questions about the regression test suite:
- Review test documentation and comments
- Check existing test patterns for guidance
- Ensure new tests follow established conventions
- Validate tests work with the automated test runner

## License

This regression test suite is part of the GretahAI ScriptWeaver project.
© 2025 Cogniron All Rights Reserved.
