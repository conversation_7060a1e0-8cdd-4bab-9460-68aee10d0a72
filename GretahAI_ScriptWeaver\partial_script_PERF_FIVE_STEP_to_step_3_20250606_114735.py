# =========================================================================
# PARTIAL SCRIPT FOR STATE BUILDING - PERF_FIVE_STEP
# =========================================================================
# Generated: 2025-06-06 11:47:35
# Purpose: Execute steps 1-3 to build browser state
# Target Step: 4
# Steps Included: 3
# 
# This script is automatically generated for script-based state building.
# It executes the necessary steps to reach the correct browser state before
# launching the interactive element selector.
# =========================================================================

import pytest
import time
import random
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Test data for partial script execution
TEST_DATA = {
    "website_url": "https://httpbin.org/forms/post"
}

@pytest.mark.order(1)
def test_step1_navigate(browser):
    """
    Step 1: Navigate to test form
    Action: navigate
    Locator: url=https://httpbin.org/forms/post
    """
    try:
        # Navigate to URL
        target_url = "https://httpbin.org/forms/post" if "https://httpbin.org/forms/post".startswith("http") else TEST_DATA.get("website_url", "https://httpbin.org/forms/post")
        browser.get(target_url)
        
        # Wait for page to load
        WebDriverWait(browser, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Additional wait for dynamic content
        time.sleep(1.0)
        
        # Brief pause for stability
        time.sleep(random.uniform(0.3, 0.7))
        
    except Exception as e:
        print(f"Exception in test_step1_navigate: {repr(e)}")
        # Take screenshot for debugging
        try:
            browser.save_screenshot(f"test_step1_navigate_failure.png")
        except:
            pass
        raise

@pytest.mark.order(2)
def test_step2_wait(browser):
    """
    Step 2: Wait for element 2
    Action: wait
    Locator: css=.loading2
    """
    try:
        # Wait for element to appear
        wait = WebDriverWait(browser, 5)
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".loading2")))
        
        # Brief pause for stability
        time.sleep(random.uniform(0.3, 0.7))
        
    except Exception as e:
        print(f"Exception in test_step2_wait: {repr(e)}")
        # Take screenshot for debugging
        try:
            browser.save_screenshot(f"test_step2_wait_failure.png")
        except:
            pass
        raise

@pytest.mark.order(3)
def test_step3_click(browser):
    """
    Step 3: Click field 3
    Action: click
    Locator: css=input[name="field3"]
    """
    try:
        # Find and click element
        wait = WebDriverWait(browser, 10)
        element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "input[name="field3"]")))
        
        # Scroll element into view
        browser.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        time.sleep(0.3)
        
        # Click element
        element.click()
        
        # Brief pause for stability
        time.sleep(random.uniform(0.3, 0.7))
        
    except Exception as e:
        print(f"Exception in test_step3_click: {repr(e)}")
        # Take screenshot for debugging
        try:
            browser.save_screenshot(f"test_step3_click_failure.png")
        except:
            pass
        raise


# =========================================================================
# BROWSER SESSION PRESERVATION
# =========================================================================
# The browser session is maintained after this script execution for use
# with the interactive element selector. The browser state should reflect
# the completion of all steps included in this partial script.
# =========================================================================
