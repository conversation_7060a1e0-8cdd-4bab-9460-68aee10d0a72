# =========================================================================
# PARTIAL SCRIPT FOR STATE BUILDING - DATA_TYPE_FLOAT_TIMEOUT
# =========================================================================
# Generated: 2025-06-06 12:03:06
# Purpose: Execute steps 1-1 to build browser state
# Target Step: 2
# Steps Included: 1
# 
# This script is automatically generated for script-based state building.
# It executes the necessary steps to reach the correct browser state before
# launching the interactive element selector.
# =========================================================================

import pytest
import time
import random
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Test data for partial script execution
TEST_DATA = {
    "website_url": "https://httpbin.org/forms/post"
}

@pytest.mark.order(1)
def test_step1_navigate(browser):
    """
    Step 1: Execute navigate
    Action: navigate
    Locator: url=https://httpbin.org/forms/post
    """
    try:
        # Navigate to URL
        target_url = "https://httpbin.org/forms/post" if "https://httpbin.org/forms/post".startswith("http") else TEST_DATA.get("website_url", "https://httpbin.org/forms/post")
        browser.get(target_url)
        
        # Wait for page to load
        WebDriverWait(browser, 15.5).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Additional wait for dynamic content
        time.sleep(1.0)
        
        # Brief pause for stability
        time.sleep(random.uniform(0.3, 0.7))
        
    except Exception as e:
        print(f"Exception in test_step1_navigate: {repr(e)}")
        # Take screenshot for debugging
        try:
            browser.save_screenshot(f"test_step1_navigate_failure.png")
        except:
            pass
        raise


# =========================================================================
# BROWSER SESSION PRESERVATION
# =========================================================================
# The browser session is maintained after this script execution for use
# with the interactive element selector. The browser state should reflect
# the completion of all steps included in this partial script.
# =========================================================================
