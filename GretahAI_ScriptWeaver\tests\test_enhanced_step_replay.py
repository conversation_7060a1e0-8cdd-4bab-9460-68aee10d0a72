#!/usr/bin/env python3
"""
Enhanced Step Replay Engine Test Script

This script tests the enhanced step replay functionality with improved error handling,
retry mechanisms, and diagnostic capabilities.

Test Scenarios:
1. Basic step replay with successful element finding
2. Element not found scenarios with retry mechanisms
3. Page state verification and timing issues
4. Alternative locator strategy fallbacks
5. Comprehensive error reporting and diagnostics

Usage:
    python tests/test_enhanced_step_replay.py
"""

import sys
import os
import time
import logging
from typing import Dict, List, Any

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_enhanced_step_replay")

def create_test_step_table() -> List[Dict[str, Any]]:
    """Create a test step table that simulates the failing scenario."""
    return [
        {
            'step_no': 1,
            'action': 'navigate',
            'locator_strategy': 'url',
            'locator': 'https://example.com',
            'test_data_param': '',
            'timeout': 10
        },
        {
            'step_no': 2,
            'action': 'type',
            'locator_strategy': 'id',
            'locator': 'userid',  # This is the failing element
            'test_data_param': 'testuser',
            'timeout': 10
        },
        {
            'step_no': 3,
            'action': 'click',
            'locator_strategy': 'id',
            'locator': 'submit-btn',
            'test_data_param': '',
            'timeout': 10
        }
    ]

def create_alternative_test_step_table() -> List[Dict[str, Any]]:
    """Create a test step table with more robust locators."""
    return [
        {
            'step_no': 1,
            'action': 'navigate',
            'locator_strategy': 'url',
            'locator': 'https://httpbin.org/forms/post',
            'test_data_param': '',
            'timeout': 15
        },
        {
            'step_no': 2,
            'action': 'type',
            'locator_strategy': 'name',
            'locator': 'custname',
            'test_data_param': 'Test Customer',
            'timeout': 15
        },
        {
            'step_no': 3,
            'action': 'type',
            'locator_strategy': 'name',
            'locator': 'custtel',
            'test_data_param': '************',
            'timeout': 15
        }
    ]

def test_enhanced_step_replay_basic():
    """Test basic enhanced step replay functionality."""
    logger.info("=== Testing Enhanced Step Replay - Basic Functionality ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from core.step_replay_engine import StepReplayEngine
        
        # Setup Chrome driver with options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # Enhanced configuration
        config = {
            'default_timeout': 15,
            'retry_attempts': 3,
            'retry_delay': 1.0,
            'page_load_timeout': 30,
            'element_interaction_delay': 0.5,
            'step_transition_delay': 1.0
        }
        
        replay_engine = StepReplayEngine(driver, config=config)
        
        # Test with alternative step table (more likely to succeed)
        step_table = create_alternative_test_step_table()
        test_data = {'custname': 'Test Customer', 'custtel': '************'}
        
        logger.info("Testing step replay with httpbin.org form...")
        success, message = replay_engine.replay_steps_to_current(
            step_table, 2, "https://httpbin.org/forms/post", test_data
        )
        
        if success:
            logger.info(f"✅ Basic test PASSED: {message}")
        else:
            logger.error(f"❌ Basic test FAILED: {message}")
            if replay_engine.last_error:
                logger.error(f"Detailed error: {replay_engine.last_error}")
        
        # Log diagnostics
        if replay_engine.step_diagnostics:
            logger.info("Step execution diagnostics:")
            for diag in replay_engine.step_diagnostics:
                status = "✅" if diag['success'] else "❌"
                logger.info(f"  Step {diag['step_no']}: {status} {diag['action']} ({diag['duration']:.2f}s)")
        
        driver.quit()
        return success
        
    except Exception as e:
        logger.error(f"Exception in basic test: {e}")
        return False

def test_enhanced_step_replay_failure_scenario():
    """Test enhanced step replay with element not found scenario."""
    logger.info("=== Testing Enhanced Step Replay - Failure Scenario ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from core.step_replay_engine import StepReplayEngine
        
        # Setup Chrome driver with options
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # Enhanced configuration with more aggressive retries
        config = {
            'default_timeout': 5,  # Shorter timeout to trigger failures faster
            'retry_attempts': 3,
            'retry_delay': 0.5,
            'page_load_timeout': 15,
            'element_interaction_delay': 0.2,
            'step_transition_delay': 0.5
        }
        
        replay_engine = StepReplayEngine(driver, config=config)
        
        # Test with original failing step table
        step_table = create_test_step_table()
        test_data = {'userid': 'testuser'}
        
        logger.info("Testing step replay with failing element scenario...")
        success, message = replay_engine.replay_steps_to_current(
            step_table, 2, "https://example.com", test_data
        )
        
        # This test expects failure, so we check for proper error handling
        if not success:
            logger.info(f"✅ Failure scenario test PASSED: Properly handled failure - {message}")
            if replay_engine.last_error:
                logger.info(f"Detailed error captured: {replay_engine.last_error}")
            
            # Verify diagnostic information was captured
            if replay_engine.step_diagnostics:
                logger.info("Diagnostic information captured:")
                for diag in replay_engine.step_diagnostics:
                    status = "✅" if diag['success'] else "❌"
                    logger.info(f"  Step {diag['step_no']}: {status} {diag['action']} ({diag['duration']:.2f}s)")
                    if not diag['success'] and diag['error_msg']:
                        logger.info(f"    Error: {diag['error_msg']}")
            
            test_result = True
        else:
            logger.warning(f"⚠️ Failure scenario test UNEXPECTED: Expected failure but got success - {message}")
            test_result = False
        
        driver.quit()
        return test_result
        
    except Exception as e:
        logger.error(f"Exception in failure scenario test: {e}")
        return False

def test_configuration_validation():
    """Test configuration parameter validation."""
    logger.info("=== Testing Configuration Validation ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from core.step_replay_engine import StepReplayEngine
        
        # Setup minimal driver
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # Test with custom configuration
        custom_config = {
            'default_timeout': 20,
            'retry_attempts': 5,
            'retry_delay': 2.0,
            'page_load_timeout': 45,
            'element_interaction_delay': 1.0,
            'step_transition_delay': 1.5
        }
        
        replay_engine = StepReplayEngine(driver, config=custom_config)
        
        # Verify configuration was applied
        assert replay_engine.default_timeout == 20
        assert replay_engine.retry_attempts == 5
        assert replay_engine.retry_delay == 2.0
        assert replay_engine.page_load_timeout == 45
        assert replay_engine.element_interaction_delay == 1.0
        assert replay_engine.step_transition_delay == 1.5
        
        logger.info("✅ Configuration validation PASSED")
        
        # Test with no configuration (should use defaults)
        replay_engine_default = StepReplayEngine(driver)
        assert replay_engine_default.default_timeout == 10  # DEFAULT_TIMEOUT
        assert replay_engine_default.retry_attempts == 3   # RETRY_ATTEMPTS
        
        logger.info("✅ Default configuration validation PASSED")
        
        driver.quit()
        return True
        
    except Exception as e:
        logger.error(f"Exception in configuration validation: {e}")
        return False

def main():
    """Run all enhanced step replay tests."""
    logger.info("Starting Enhanced Step Replay Engine Tests")
    
    test_results = []
    
    # Run all tests
    test_results.append(("Basic Functionality", test_enhanced_step_replay_basic()))
    test_results.append(("Failure Scenario", test_enhanced_step_replay_failure_scenario()))
    test_results.append(("Configuration Validation", test_configuration_validation()))
    
    # Report results
    logger.info("\n" + "="*60)
    logger.info("ENHANCED STEP REPLAY TEST RESULTS")
    logger.info("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All enhanced step replay tests PASSED!")
        return 0
    else:
        logger.error(f"💥 {total - passed} test(s) FAILED!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
