#!/usr/bin/env python3
"""
Cross-Browser Compatibility Regression Tests

This module tests script-based state building across different browser versions
and configurations to ensure consistent behavior and compatibility.

Test Coverage:
- Chrome, Firefox, Edge browser compatibility
- WebDriver compatibility and browser-specific behaviors
- Headless vs. visible browser modes
- Browser version differences
- Driver configuration variations
"""

import sys
import os
import pytest
import logging
import time
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("regression.cross_browser")

class TestCrossBrowserCompatibility:
    """Cross-browser compatibility tests for script-based state building."""
    
    @pytest.fixture
    def sample_step_table(self):
        """Sample step table for cross-browser testing."""
        return [
            {
                'step_no': '1',
                'action': 'navigate',
                'locator_strategy': 'url',
                'locator': 'https://httpbin.org/forms/post',
                'test_data_param': '',
                'timeout': 20,
                'step_description': 'Navigate to test form'
            },
            {
                'step_no': '2',
                'action': 'type',
                'locator_strategy': 'name',
                'locator': 'custname',
                'test_data_param': 'Cross Browser Test',
                'timeout': 15,
                'step_description': 'Enter test data'
            }
        ]
    
    @pytest.fixture
    def browser_configs(self):
        """Browser configuration matrix for testing."""
        return [
            {
                'browser': 'chrome',
                'headless': True,
                'options': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
            },
            {
                'browser': 'chrome',
                'headless': False,
                'options': ['--no-sandbox', '--disable-dev-shm-usage']
            },
            {
                'browser': 'firefox',
                'headless': True,
                'options': ['--width=1920', '--height=1080']
            },
            {
                'browser': 'firefox',
                'headless': False,
                'options': []
            }
        ]
    
    def test_chrome_headless_compatibility(self, sample_step_table):
        """Test script-based state building with Chrome headless."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test with Chrome-specific configuration
        config = {
            'browser_type': 'chrome',
            'headless': True,
            'script_timeout': 60
        }
        
        state_builder = ScriptBasedStateBuilder(config=config)
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=sample_step_table,
            current_step_index=1,
            website_url="https://httpbin.org/forms/post",
            test_data={'custname': 'Chrome Headless Test'},
            test_case_id="CHROME_HEADLESS_001"
        )
        
        assert success, f"Chrome headless test failed: {message}"
        assert state_builder.execution_results is not None
        assert state_builder.execution_results.get('returncode') == 0
        
        # Cleanup
        state_builder.cleanup()
    
    def test_chrome_visible_compatibility(self, sample_step_table):
        """Test script-based state building with Chrome visible mode."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        config = {
            'browser_type': 'chrome',
            'headless': False,
            'script_timeout': 60
        }
        
        state_builder = ScriptBasedStateBuilder(config=config)
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=sample_step_table,
            current_step_index=1,
            website_url="https://httpbin.org/forms/post",
            test_data={'custname': 'Chrome Visible Test'},
            test_case_id="CHROME_VISIBLE_001"
        )
        
        assert success, f"Chrome visible test failed: {message}"
        
        # Cleanup
        state_builder.cleanup()
    
    @pytest.mark.skipif(not os.getenv('TEST_FIREFOX'), reason="Firefox testing disabled")
    def test_firefox_compatibility(self, sample_step_table):
        """Test script-based state building with Firefox."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        config = {
            'browser_type': 'firefox',
            'headless': True,
            'script_timeout': 90  # Firefox might be slower
        }
        
        state_builder = ScriptBasedStateBuilder(config=config)
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=sample_step_table,
            current_step_index=1,
            website_url="https://httpbin.org/forms/post",
            test_data={'custname': 'Firefox Test'},
            test_case_id="FIREFOX_001"
        )
        
        # Firefox might have different behavior, so we're more lenient
        assert isinstance(success, bool), "Success should be boolean"
        assert isinstance(message, str), "Message should be string"
        
        # Cleanup
        state_builder.cleanup()
    
    @pytest.mark.skipif(not os.getenv('TEST_EDGE'), reason="Edge testing disabled")
    def test_edge_compatibility(self, sample_step_table):
        """Test script-based state building with Edge."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        config = {
            'browser_type': 'edge',
            'headless': True,
            'script_timeout': 90
        }
        
        state_builder = ScriptBasedStateBuilder(config=config)
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=sample_step_table,
            current_step_index=1,
            website_url="https://httpbin.org/forms/post",
            test_data={'custname': 'Edge Test'},
            test_case_id="EDGE_001"
        )
        
        # Edge might have different behavior
        assert isinstance(success, bool), "Success should be boolean"
        assert isinstance(message, str), "Message should be string"
        
        # Cleanup
        state_builder.cleanup()
    
    def test_webdriver_compatibility_matrix(self, sample_step_table):
        """Test WebDriver compatibility across different configurations."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test different WebDriver configurations
        configs = [
            {'script_timeout': 30, 'cleanup_scripts': True},
            {'script_timeout': 60, 'cleanup_scripts': False},
            {'script_timeout': 90, 'verbose_logging': True}
        ]
        
        for i, config in enumerate(configs):
            state_builder = ScriptBasedStateBuilder(config=config)
            
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=sample_step_table[:1],  # Just navigation
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"WEBDRIVER_COMPAT_{i+1:03d}"
            )
            
            assert success, f"WebDriver compatibility test {i+1} failed: {message}"
            
            # Verify configuration was applied
            assert state_builder.config == config
            
            # Cleanup
            state_builder.cleanup()
    
    def test_browser_specific_behaviors(self, sample_step_table):
        """Test browser-specific behaviors and workarounds."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test with browser-specific step configurations
        browser_specific_steps = [
            {
                'step_no': '1',
                'action': 'navigate',
                'locator_strategy': 'url',
                'locator': 'https://httpbin.org/forms/post',
                'test_data_param': '',
                'timeout': 30,  # Longer timeout for cross-browser compatibility
                'step_description': 'Navigate with extended timeout'
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=browser_specific_steps,
            current_step_index=1,
            website_url="https://httpbin.org/forms/post",
            test_data={},
            test_case_id="BROWSER_SPECIFIC_001"
        )
        
        assert success, f"Browser-specific behavior test failed: {message}"
        
        # Verify the generated script includes browser-specific considerations
        if state_builder.partial_script_path and os.path.exists(state_builder.partial_script_path):
            with open(state_builder.partial_script_path, 'r') as f:
                script_content = f.read()
                
            # Check for browser compatibility elements
            assert 'WebDriverWait' in script_content
            assert 'EC.' in script_content  # Expected conditions
            assert 'timeout' in script_content.lower()
        
        # Cleanup
        state_builder.cleanup()
    
    def test_headless_vs_visible_mode_consistency(self, sample_step_table):
        """Test consistency between headless and visible browser modes."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        results = {}
        
        # Test both headless and visible modes
        for mode in ['headless', 'visible']:
            config = {
                'browser_mode': mode,
                'script_timeout': 60
            }
            
            state_builder = ScriptBasedStateBuilder(config=config)
            
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=sample_step_table[:1],  # Just navigation
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"MODE_CONSISTENCY_{mode.upper()}"
            )
            
            results[mode] = {
                'success': success,
                'message': message,
                'execution_time': state_builder.execution_results.get('execution_time', 0) if state_builder.execution_results else 0
            }
            
            # Cleanup
            state_builder.cleanup()
        
        # Compare results between modes
        assert results['headless']['success'] == results['visible']['success'], \
               "Headless and visible modes should have consistent success rates"
        
        # Both should succeed or both should fail for the same reasons
        if results['headless']['success'] and results['visible']['success']:
            # Both succeeded - check execution times are reasonable
            headless_time = results['headless']['execution_time']
            visible_time = results['visible']['execution_time']
            
            # Headless should generally be faster, but allow for variance
            assert headless_time > 0, "Headless execution time should be positive"
            assert visible_time > 0, "Visible execution time should be positive"
    
    def test_driver_version_compatibility(self, sample_step_table):
        """Test compatibility with different WebDriver versions."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test with different driver configurations
        driver_configs = [
            {'driver_version': 'latest', 'implicit_wait': 10},
            {'driver_version': 'stable', 'implicit_wait': 15},
            {'driver_version': 'auto', 'implicit_wait': 5}
        ]
        
        for config in driver_configs:
            state_builder = ScriptBasedStateBuilder(config=config)
            
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=sample_step_table[:1],
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"DRIVER_VERSION_{config['driver_version'].upper()}"
            )
            
            # Should handle different driver versions gracefully
            assert isinstance(success, bool), f"Success should be boolean for {config}"
            assert isinstance(message, str), f"Message should be string for {config}"
            
            # Cleanup
            state_builder.cleanup()
    
    def test_browser_options_compatibility(self, sample_step_table):
        """Test compatibility with various browser options."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test with different browser option sets
        option_sets = [
            {
                'name': 'minimal',
                'options': ['--no-sandbox', '--disable-dev-shm-usage']
            },
            {
                'name': 'performance',
                'options': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--disable-extensions']
            },
            {
                'name': 'compatibility',
                'options': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-web-security', '--allow-running-insecure-content']
            }
        ]
        
        for option_set in option_sets:
            config = {
                'browser_options': option_set['options'],
                'script_timeout': 45
            }
            
            state_builder = ScriptBasedStateBuilder(config=config)
            
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=sample_step_table[:1],
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"BROWSER_OPTIONS_{option_set['name'].upper()}"
            )
            
            assert success, f"Browser options test failed for {option_set['name']}: {message}"
            
            # Cleanup
            state_builder.cleanup()
    
    def test_cross_browser_script_generation_consistency(self, sample_step_table):
        """Test that script generation is consistent across browser types."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        generated_scripts = {}
        
        # Generate scripts for different browser configurations
        browser_types = ['chrome', 'firefox', 'edge']
        
        for browser_type in browser_types:
            config = {'target_browser': browser_type}
            state_builder = ScriptBasedStateBuilder(config=config)
            
            # Generate script content
            script_content = state_builder._generate_partial_script(
                step_table=sample_step_table,
                current_step_index=2,
                website_url="https://httpbin.org/forms/post",
                test_data={'custname': 'Test'},
                test_case_id=f"SCRIPT_GEN_{browser_type.upper()}"
            )
            
            generated_scripts[browser_type] = script_content
            
            # Verify script was generated
            assert script_content is not None, f"Script generation failed for {browser_type}"
            assert len(script_content) > 0, f"Empty script generated for {browser_type}"
            
            # Check for essential elements
            assert 'import pytest' in script_content
            assert 'def test_step1_navigate' in script_content
            assert 'WebDriverWait' in script_content
        
        # Compare scripts for consistency
        chrome_script = generated_scripts.get('chrome', '')
        firefox_script = generated_scripts.get('firefox', '')
        
        if chrome_script and firefox_script:
            # Core structure should be similar
            assert 'import pytest' in chrome_script and 'import pytest' in firefox_script
            assert 'TEST_DATA' in chrome_script and 'TEST_DATA' in firefox_script
            
            # Both should have similar function definitions
            chrome_functions = [line for line in chrome_script.split('\n') if line.startswith('def test_')]
            firefox_functions = [line for line in firefox_script.split('\n') if line.startswith('def test_')]
            
            assert len(chrome_functions) == len(firefox_functions), \
                   "Chrome and Firefox scripts should have same number of test functions"

if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
