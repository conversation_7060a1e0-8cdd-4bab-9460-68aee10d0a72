#!/usr/bin/env python3
"""
Script-Based State Builder Test Script

This script tests the script-based state building functionality that replaces
manual step replay with generated script execution for better reliability.

Test Scenarios:
1. Basic partial script generation and execution
2. Complex multi-step scenarios with dynamic content
3. Error handling and fallback mechanisms
4. Performance comparison with manual step replay
5. Integration with interactive element selector

Usage:
    python tests/test_script_based_state_builder.py
"""

import sys
import os
import time
import logging
import tempfile
from typing import Dict, List, Any

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_script_based_state_builder")

def create_test_step_table() -> List[Dict[str, Any]]:
    """Create a test step table for script-based state building."""
    return [
        {
            'step_no': '1',
            'step_type': 'ui',
            'action': 'navigate',
            'locator_strategy': 'url',
            'locator': 'https://httpbin.org/forms/post',
            'test_data_param': '',
            'expected_result': 'Form page loads',
            'assertion_type': 'page_title',
            'condition': '',
            'timeout': 15,
            'step_description': 'Navigate to the test form page'
        },
        {
            'step_no': '2',
            'step_type': 'ui',
            'action': 'type',
            'locator_strategy': 'name',
            'locator': 'custname',
            'test_data_param': 'Test Customer',
            'expected_result': 'Customer name entered',
            'assertion_type': 'element_value',
            'condition': '',
            'timeout': 10,
            'step_description': 'Enter customer name in the form field'
        },
        {
            'step_no': '3',
            'step_type': 'ui',
            'action': 'type',
            'locator_strategy': 'name',
            'locator': 'custtel',
            'test_data_param': '************',
            'expected_result': 'Phone number entered',
            'assertion_type': 'element_value',
            'condition': '',
            'timeout': 10,
            'step_description': 'Enter customer phone number'
        },
        {
            'step_no': '4',
            'step_type': 'ui',
            'action': 'click',
            'locator_strategy': 'css',
            'locator': 'input[type="submit"]',
            'test_data_param': '',
            'expected_result': 'Form submitted',
            'assertion_type': 'page_url',
            'condition': '',
            'timeout': 10,
            'step_description': 'Submit the form'
        }
    ]

def test_partial_script_generation():
    """Test partial script generation functionality."""
    logger.info("=== Testing Partial Script Generation ===")
    
    try:
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Create test data
        step_table = create_test_step_table()
        current_step_index = 3  # Generate script for steps 1-2
        website_url = "https://httpbin.org/forms/post"
        test_data = {
            'custname': 'Test Customer',
            'custtel': '************'
        }
        test_case_id = "TC_001"
        
        # Initialize state builder
        state_builder = ScriptBasedStateBuilder()
        
        # Test partial script generation
        logger.info(f"Generating partial script for steps 1-{current_step_index}")
        partial_script = state_builder._generate_partial_script(
            step_table, current_step_index, website_url, test_data, test_case_id
        )
        
        if partial_script:
            logger.info("✅ Partial script generation PASSED")
            logger.info(f"Generated script length: {len(partial_script)} characters")
            
            # Verify script contains expected elements
            expected_elements = [
                "import pytest",
                "test_step1_navigate",
                "test_step2_type",
                "TEST_DATA",
                "custname",
                "custtel"
            ]
            
            missing_elements = []
            for element in expected_elements:
                if element not in partial_script:
                    missing_elements.append(element)
            
            if missing_elements:
                logger.warning(f"⚠️ Missing elements in generated script: {missing_elements}")
                return False
            else:
                logger.info("✅ All expected elements found in generated script")
                return True
        else:
            logger.error("❌ Partial script generation FAILED: No script generated")
            return False
            
    except Exception as e:
        logger.error(f"❌ Partial script generation FAILED: {e}")
        return False

def test_script_execution():
    """Test script execution functionality."""
    logger.info("=== Testing Script Execution ===")
    
    try:
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Create test data
        step_table = create_test_step_table()
        current_step_index = 2  # Execute steps 1-1 (just navigation)
        website_url = "https://httpbin.org/forms/post"
        test_data = {
            'custname': 'Test Customer',
            'custtel': '************'
        }
        test_case_id = "TC_001"
        
        # Initialize state builder
        state_builder = ScriptBasedStateBuilder()
        
        # Test script-based state building
        logger.info(f"Testing script-based state building for step {current_step_index}")
        success, message, browser_info = state_builder.build_state_with_script(
            step_table, current_step_index, website_url, test_data, test_case_id
        )
        
        if success:
            logger.info(f"✅ Script execution PASSED: {message}")
            if browser_info:
                logger.info(f"Browser info: {browser_info}")
            return True
        else:
            logger.error(f"❌ Script execution FAILED: {message}")
            if state_builder.last_error:
                logger.error(f"Detailed error: {state_builder.last_error}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Script execution FAILED: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid step data."""
    logger.info("=== Testing Error Handling ===")
    
    try:
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Create invalid test data
        invalid_step_table = [
            {
                'step_no': '1',
                'action': 'invalid_action',
                'locator_strategy': 'invalid_strategy',
                'locator': 'invalid_locator',
                'test_data_param': '',
                'timeout': 5
            }
        ]
        
        current_step_index = 1
        website_url = "https://invalid-url-that-does-not-exist.com"
        test_data = {}
        test_case_id = "TC_INVALID"
        
        # Initialize state builder
        state_builder = ScriptBasedStateBuilder()
        
        # Test with invalid data
        logger.info("Testing error handling with invalid step data")
        success, message, browser_info = state_builder.build_state_with_script(
            invalid_step_table, current_step_index, website_url, test_data, test_case_id
        )
        
        if not success:
            logger.info(f"✅ Error handling PASSED: Properly handled invalid data - {message}")
            return True
        else:
            logger.warning(f"⚠️ Error handling UNEXPECTED: Expected failure but got success - {message}")
            return False
            
    except Exception as e:
        logger.info(f"✅ Error handling PASSED: Exception properly caught - {e}")
        return True

def test_configuration_options():
    """Test configuration options for the state builder."""
    logger.info("=== Testing Configuration Options ===")
    
    try:
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test with custom configuration
        custom_config = {
            'script_timeout': 60,
            'cleanup_scripts': False,
            'verbose_logging': True
        }
        
        state_builder = ScriptBasedStateBuilder(config=custom_config)
        
        # Verify configuration was applied
        assert state_builder.config == custom_config
        
        logger.info("✅ Configuration options PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration options FAILED: {e}")
        return False

def test_script_cleanup():
    """Test script cleanup functionality."""
    logger.info("=== Testing Script Cleanup ===")
    
    try:
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Create test data
        step_table = create_test_step_table()[:1]  # Just one step
        current_step_index = 1
        website_url = "https://httpbin.org/forms/post"
        test_data = {}
        test_case_id = "TC_CLEANUP"
        
        # Initialize state builder
        state_builder = ScriptBasedStateBuilder()
        
        # Generate and save a script
        partial_script = state_builder._generate_partial_script(
            step_table, current_step_index, website_url, test_data, test_case_id
        )
        
        if partial_script:
            script_path = state_builder._save_partial_script(
                partial_script, test_case_id, current_step_index
            )
            
            if script_path and os.path.exists(script_path):
                logger.info(f"Script saved to: {script_path}")
                
                # Test cleanup
                state_builder.partial_script_path = script_path
                state_builder.cleanup()
                
                if not os.path.exists(script_path):
                    logger.info("✅ Script cleanup PASSED")
                    return True
                else:
                    logger.error("❌ Script cleanup FAILED: File still exists")
                    return False
            else:
                logger.error("❌ Script cleanup test FAILED: Could not save script")
                return False
        else:
            logger.error("❌ Script cleanup test FAILED: Could not generate script")
            return False
            
    except Exception as e:
        logger.error(f"❌ Script cleanup FAILED: {e}")
        return False

def test_integration_with_interactive_selector():
    """Test integration with interactive element selector."""
    logger.info("=== Testing Integration with Interactive Selector ===")
    
    try:
        # Test that the interactive selector can accept the new parameters
        from core.interactive_selector import select_element_interactively
        
        # Check function signature
        import inspect
        sig = inspect.signature(select_element_interactively)
        
        expected_params = [
            'use_script_based_state_building',
            'test_case_id'
        ]
        
        actual_params = list(sig.parameters.keys())
        
        missing_params = []
        for param in expected_params:
            if param not in actual_params:
                missing_params.append(param)
        
        if missing_params:
            logger.error(f"❌ Integration test FAILED: Missing parameters: {missing_params}")
            return False
        else:
            logger.info("✅ Integration test PASSED: All required parameters present")
            return True
            
    except Exception as e:
        logger.error(f"❌ Integration test FAILED: {e}")
        return False

def main():
    """Run all script-based state builder tests."""
    logger.info("Starting Script-Based State Builder Tests")
    
    test_results = []
    
    # Run all tests
    test_results.append(("Partial Script Generation", test_partial_script_generation()))
    test_results.append(("Script Execution", test_script_execution()))
    test_results.append(("Error Handling", test_error_handling()))
    test_results.append(("Configuration Options", test_configuration_options()))
    test_results.append(("Script Cleanup", test_script_cleanup()))
    test_results.append(("Integration with Interactive Selector", test_integration_with_interactive_selector()))
    
    # Report results
    logger.info("\n" + "="*60)
    logger.info("SCRIPT-BASED STATE BUILDER TEST RESULTS")
    logger.info("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All script-based state builder tests PASSED!")
        logger.info("✅ Script-based state building is ready for production use!")
        return 0
    else:
        logger.error(f"💥 {total - passed} test(s) FAILED!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
