#!/usr/bin/env python3
"""
End-to-End Regression Tests for Script-Based State Building

This module provides comprehensive end-to-end regression testing for the script-based
state building functionality, ensuring complete workflow integrity from step table
input through script generation, execution, and interactive element selection.

Test Coverage:
- Complete workflow validation
- Integration between ScriptBasedStateBuilder and interactive selector
- Fallback mechanism testing (script-based → manual replay → base URL)
- State persistence and browser session management
- Error propagation and user feedback
"""

import sys
import os
import time
import logging
import pytest
import tempfile
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("regression.end_to_end")

class TestEndToEndRegression:
    """End-to-end regression tests for script-based state building."""
    
    @pytest.fixture
    def sample_step_table(self):
        """Sample step table for testing."""
        return [
            {
                'step_no': '1',
                'step_type': 'ui',
                'action': 'navigate',
                'locator_strategy': 'url',
                'locator': 'https://httpbin.org/forms/post',
                'test_data_param': '',
                'expected_result': 'Form page loads',
                'timeout': 15,
                'step_description': 'Navigate to test form'
            },
            {
                'step_no': '2',
                'step_type': 'ui',
                'action': 'type',
                'locator_strategy': 'name',
                'locator': 'custname',
                'test_data_param': 'Test Customer',
                'expected_result': 'Name entered',
                'timeout': 10,
                'step_description': 'Enter customer name'
            },
            {
                'step_no': '3',
                'step_type': 'ui',
                'action': 'type',
                'locator_strategy': 'name',
                'locator': 'custtel',
                'test_data_param': '************',
                'expected_result': 'Phone entered',
                'timeout': 10,
                'step_description': 'Enter phone number'
            }
        ]
    
    @pytest.fixture
    def sample_test_data(self):
        """Sample test data for parameter substitution."""
        return {
            'custname': 'Test Customer',
            'custtel': '************',
            'website_url': 'https://httpbin.org/forms/post'
        }
    
    def test_complete_workflow_script_based_success(self, sample_step_table, sample_test_data):
        """Test complete workflow with successful script-based state building."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Initialize state builder
        state_builder = ScriptBasedStateBuilder()
        
        # Test script generation and execution
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=sample_step_table,
            current_step_index=2,  # Execute steps 1-1 (navigation only)
            website_url="https://httpbin.org/forms/post",
            test_data=sample_test_data,
            test_case_id="E2E_TEST_001"
        )
        
        # Verify successful execution
        assert success, f"Script-based state building failed: {message}"
        assert browser_info is not None, "Browser info should be provided"
        assert "E2E_TEST_001" in message or "partial_script" in str(browser_info)
        
        # Verify execution results
        assert state_builder.execution_results is not None
        assert state_builder.execution_results.get('returncode') == 0
        assert state_builder.execution_results.get('execution_time') > 0
        
        # Cleanup
        state_builder.cleanup()
    
    def test_complete_workflow_with_fallback_chain(self, sample_step_table, sample_test_data):
        """Test complete workflow with fallback mechanism activation."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Create invalid step table to trigger fallback
        invalid_step_table = [
            {
                'step_no': '1',
                'action': 'navigate',
                'locator_strategy': 'url',
                'locator': 'https://invalid-domain-that-does-not-exist-12345.com',
                'test_data_param': '',
                'timeout': 5  # Short timeout to trigger failure
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        # Test with invalid data (should trigger fallback)
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=invalid_step_table,
            current_step_index=1,
            website_url="https://invalid-domain-that-does-not-exist-12345.com",
            test_data={},
            test_case_id="E2E_FALLBACK_001"
        )
        
        # Script execution might still succeed even with invalid URL
        # The important thing is that the system handles it gracefully
        assert isinstance(success, bool), "Success should be a boolean"
        assert isinstance(message, str), "Message should be a string"
        
        # Cleanup
        state_builder.cleanup()
    
    @patch('core.interactive_selector.select_element_interactively')
    def test_integration_with_interactive_selector(self, mock_selector, sample_step_table, sample_test_data):
        """Test integration between state builder and interactive selector."""
        from core.interactive_selector import select_element_interactively
        
        # Mock the interactive selector to return a sample element
        mock_selector.return_value = {
            'locator_strategy': 'id',
            'locator': 'test-element',
            'element_type': 'input',
            'confidence': 0.95
        }
        
        # Test the integration
        result = select_element_interactively(
            url="https://httpbin.org/forms/post",
            replay_steps=True,
            step_table=sample_step_table,
            current_step_index=2,
            test_data=sample_test_data,
            use_script_based_state_building=True,
            test_case_id="E2E_INTEGRATION_001"
        )
        
        # Verify the call was made with correct parameters
        mock_selector.assert_called_once()
        call_args = mock_selector.call_args
        
        assert call_args[1]['use_script_based_state_building'] == True
        assert call_args[1]['test_case_id'] == "E2E_INTEGRATION_001"
        assert call_args[1]['replay_steps'] == True
        assert call_args[1]['current_step_index'] == 2
        
        # Verify return value
        assert result is not None
        assert result['locator_strategy'] == 'id'
    
    def test_state_persistence_across_components(self, sample_step_table, sample_test_data):
        """Test state persistence between script execution and element selection."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        state_builder = ScriptBasedStateBuilder()
        
        # Execute script-based state building
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=sample_step_table,
            current_step_index=2,
            website_url="https://httpbin.org/forms/post",
            test_data=sample_test_data,
            test_case_id="E2E_PERSISTENCE_001"
        )
        
        # Verify state information is preserved
        assert success, f"State building failed: {message}"
        
        # Check that execution results are preserved
        execution_results = state_builder.execution_results
        assert execution_results is not None
        assert 'returncode' in execution_results
        assert 'execution_time' in execution_results
        assert 'script_path' in execution_results
        
        # Verify script path exists (before cleanup)
        if state_builder.partial_script_path:
            assert os.path.exists(state_builder.partial_script_path)
        
        # Cleanup
        state_builder.cleanup()
        
        # Verify cleanup worked
        if state_builder.partial_script_path:
            assert not os.path.exists(state_builder.partial_script_path)
    
    def test_error_propagation_and_user_feedback(self, sample_step_table):
        """Test error propagation and user feedback mechanisms."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        state_builder = ScriptBasedStateBuilder()
        
        # Test with None step table (should fail gracefully)
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=None,
            current_step_index=1,
            website_url="https://example.com",
            test_data={},
            test_case_id="E2E_ERROR_001"
        )
        
        # Should handle gracefully
        assert isinstance(success, bool)
        assert isinstance(message, str)
        assert len(message) > 0, "Error message should not be empty"
        
        # Test with empty step table
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=[],
            current_step_index=0,
            website_url="https://example.com",
            test_data={},
            test_case_id="E2E_ERROR_002"
        )
        
        # Should handle empty step table gracefully
        assert isinstance(success, bool)
        assert isinstance(message, str)
    
    def test_progress_callback_functionality(self, sample_step_table, sample_test_data):
        """Test progress callback functionality during state building."""
        from core.interactive_selector import select_element_interactively
        
        # Track progress callback calls
        progress_calls = []
        
        def progress_callback(message):
            progress_calls.append(message)
        
        # Mock the interactive selector to avoid actual browser interaction
        with patch('core.interactive_selector.launch_interactive_selector') as mock_launch:
            mock_launch.return_value = {'test': 'element'}
            
            # Test with progress callback
            result = select_element_interactively(
                url="https://httpbin.org/forms/post",
                replay_steps=True,
                step_table=sample_step_table,
                current_step_index=2,
                test_data=sample_test_data,
                progress_callback=progress_callback,
                use_script_based_state_building=True,
                test_case_id="E2E_PROGRESS_001"
            )
            
            # Verify progress callbacks were made
            assert len(progress_calls) > 0, "Progress callbacks should have been made"
            
            # Check for expected progress messages
            progress_text = " ".join(progress_calls)
            assert any(keyword in progress_text.lower() for keyword in 
                      ['script', 'building', 'state', 'executing']), \
                   f"Expected progress keywords not found in: {progress_calls}"
    
    def test_browser_session_management(self, sample_step_table, sample_test_data):
        """Test browser session management during state building."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        state_builder = ScriptBasedStateBuilder()
        
        # Test multiple sequential state building operations
        for i in range(2):
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=sample_step_table[:1],  # Just navigation step
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data=sample_test_data,
                test_case_id=f"E2E_SESSION_{i+1:03d}"
            )
            
            assert success, f"Session test {i+1} failed: {message}"
            
            # Verify each execution has its own results
            assert state_builder.execution_results is not None
            assert state_builder.execution_results.get('returncode') == 0
            
            # Brief pause between executions
            time.sleep(0.5)
        
        # Cleanup
        state_builder.cleanup()
    
    def test_test_case_id_propagation(self, sample_step_table, sample_test_data):
        """Test test case ID propagation through the system."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        test_case_id = "E2E_PROPAGATION_TEST_12345"
        state_builder = ScriptBasedStateBuilder()
        
        # Execute state building with specific test case ID
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=sample_step_table[:1],
            current_step_index=1,
            website_url="https://httpbin.org/forms/post",
            test_data=sample_test_data,
            test_case_id=test_case_id
        )
        
        assert success, f"Test case ID propagation test failed: {message}"
        
        # Verify test case ID appears in generated script path
        if state_builder.partial_script_path:
            script_filename = os.path.basename(state_builder.partial_script_path)
            assert test_case_id in script_filename, \
                   f"Test case ID not found in script filename: {script_filename}"
        
        # Verify test case ID appears in browser info or execution results
        execution_results = state_builder.execution_results
        if execution_results and 'script_path' in execution_results:
            script_path = execution_results['script_path']
            assert test_case_id in script_path, \
                   f"Test case ID not found in execution results script path: {script_path}"
        
        # Cleanup
        state_builder.cleanup()

if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
