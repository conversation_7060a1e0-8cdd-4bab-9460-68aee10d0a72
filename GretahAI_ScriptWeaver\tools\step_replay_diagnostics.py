#!/usr/bin/env python3
"""
Step Replay Diagnostics Tool

This tool provides comprehensive diagnostics for step replay failures,
helping to identify and troubleshoot issues with element finding,
timing problems, and page state verification.

Features:
- Element availability analysis
- Page state verification
- Locator strategy testing
- Timing and synchronization diagnostics
- Alternative locator suggestions

Usage:
    python tools/step_replay_diagnostics.py --url <website_url> --step-data <step_json>
    python tools/step_replay_diagnostics.py --interactive
"""

import sys
import os
import json
import time
import argparse
import logging
from typing import Dict, List, Any, Optional

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("step_replay_diagnostics")

class StepReplayDiagnostics:
    """Comprehensive diagnostics for step replay issues."""
    
    def __init__(self, driver=None):
        """Initialize diagnostics with optional WebDriver."""
        self.driver = driver
        self.diagnostic_results = {}
        
    def analyze_page_state(self, url: str) -> Dict[str, Any]:
        """Analyze page state and readiness for interaction."""
        logger.info(f"Analyzing page state for: {url}")
        
        if not self.driver:
            return {"error": "No WebDriver available for analysis"}
        
        try:
            # Navigate to the page
            self.driver.get(url)
            time.sleep(2)
            
            results = {
                "url": url,
                "current_url": self.driver.current_url,
                "title": self.driver.title,
                "ready_state": None,
                "jquery_ready": None,
                "page_load_time": None,
                "element_counts": {},
                "interactive_elements": []
            }
            
            # Check document ready state
            start_time = time.time()
            try:
                results["ready_state"] = self.driver.execute_script("return document.readyState")
                load_time = time.time() - start_time
                results["page_load_time"] = f"{load_time:.2f}s"
            except Exception as e:
                results["ready_state_error"] = str(e)
            
            # Check jQuery if available
            try:
                jquery_active = self.driver.execute_script(
                    "return typeof jQuery !== 'undefined' ? jQuery.active : 'not_available'"
                )
                results["jquery_ready"] = jquery_active == 0 if jquery_active != 'not_available' else None
            except Exception as e:
                results["jquery_error"] = str(e)
            
            # Count different types of elements
            element_types = ['input', 'button', 'select', 'textarea', 'a', 'form']
            for elem_type in element_types:
                try:
                    count = len(self.driver.find_elements_by_tag_name(elem_type))
                    results["element_counts"][elem_type] = count
                except Exception:
                    results["element_counts"][elem_type] = "error"
            
            # Find interactive elements with IDs
            try:
                interactive_elements = self.driver.execute_script("""
                    var elements = [];
                    var interactiveTypes = ['input', 'button', 'select', 'textarea', 'a'];
                    
                    interactiveTypes.forEach(function(type) {
                        var elems = document.getElementsByTagName(type);
                        for (var i = 0; i < elems.length; i++) {
                            var elem = elems[i];
                            if (elem.id || elem.name || elem.className) {
                                elements.push({
                                    tag: elem.tagName.toLowerCase(),
                                    id: elem.id || '',
                                    name: elem.getAttribute('name') || '',
                                    className: elem.className || '',
                                    type: elem.getAttribute('type') || '',
                                    visible: elem.offsetParent !== null
                                });
                            }
                        }
                    });
                    
                    return elements.slice(0, 20); // Limit to first 20
                """)
                results["interactive_elements"] = interactive_elements
            except Exception as e:
                results["interactive_elements_error"] = str(e)
            
            return results
            
        except Exception as e:
            return {"error": f"Failed to analyze page state: {str(e)}"}
    
    def test_element_locators(self, locator_strategy: str, locator: str, 
                            timeout: int = 10) -> Dict[str, Any]:
        """Test different approaches to finding an element."""
        logger.info(f"Testing element locators: {locator_strategy}={locator}")
        
        if not self.driver:
            return {"error": "No WebDriver available for testing"}
        
        results = {
            "locator_strategy": locator_strategy,
            "locator": locator,
            "timeout": timeout,
            "tests": {}
        }
        
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # Test different wait conditions
        wait_conditions = [
            ("presence_of_element_located", EC.presence_of_element_located),
            ("visibility_of_element_located", EC.visibility_of_element_located),
            ("element_to_be_clickable", EC.element_to_be_clickable)
        ]
        
        # Convert locator strategy to By locator
        by_locator = self._get_by_locator(locator_strategy, locator)
        if not by_locator:
            results["error"] = f"Unknown locator strategy: {locator_strategy}"
            return results
        
        for condition_name, condition_func in wait_conditions:
            try:
                start_time = time.time()
                wait = WebDriverWait(self.driver, timeout)
                element = wait.until(condition_func(by_locator))
                duration = time.time() - start_time
                
                results["tests"][condition_name] = {
                    "success": True,
                    "duration": f"{duration:.2f}s",
                    "element_info": {
                        "tag": element.tag_name,
                        "displayed": element.is_displayed(),
                        "enabled": element.is_enabled(),
                        "location": element.location,
                        "size": element.size
                    }
                }
            except Exception as e:
                results["tests"][condition_name] = {
                    "success": False,
                    "error": str(e),
                    "duration": f"{timeout}s (timeout)"
                }
        
        # Test alternative locator strategies
        if locator_strategy.lower() == 'id':
            # Try CSS selector alternative
            css_locator = f"#{locator}"
            results["alternatives"] = {}
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, css_locator)
                results["alternatives"]["css_selector"] = {
                    "locator": css_locator,
                    "found_count": len(elements),
                    "success": len(elements) > 0
                }
            except Exception as e:
                results["alternatives"]["css_selector"] = {
                    "locator": css_locator,
                    "error": str(e)
                }
        
        return results
    
    def _get_by_locator(self, locator_strategy: str, locator: str):
        """Convert locator strategy to Selenium By locator."""
        from selenium.webdriver.common.by import By
        
        strategy = locator_strategy.lower()
        if strategy == 'id':
            return (By.ID, locator)
        elif strategy == 'name':
            return (By.NAME, locator)
        elif strategy in ['css', 'css_selector']:
            return (By.CSS_SELECTOR, locator)
        elif strategy == 'xpath':
            return (By.XPATH, locator)
        elif strategy == 'class':
            return (By.CLASS_NAME, locator)
        elif strategy == 'tag':
            return (By.TAG_NAME, locator)
        elif strategy == 'link_text':
            return (By.LINK_TEXT, locator)
        elif strategy == 'partial_link_text':
            return (By.PARTIAL_LINK_TEXT, locator)
        else:
            return None
    
    def suggest_alternative_locators(self, target_element_info: Dict[str, Any]) -> List[Dict[str, str]]:
        """Suggest alternative locator strategies for an element."""
        suggestions = []
        
        # Extract element information
        element_id = target_element_info.get('id', '')
        element_name = target_element_info.get('name', '')
        element_class = target_element_info.get('className', '')
        element_tag = target_element_info.get('tag', '')
        element_type = target_element_info.get('type', '')
        
        # ID-based suggestions
        if element_id:
            suggestions.append({
                "strategy": "id",
                "locator": element_id,
                "reliability": "high",
                "description": f"Direct ID selector"
            })
            suggestions.append({
                "strategy": "css",
                "locator": f"#{element_id}",
                "reliability": "high",
                "description": f"CSS ID selector"
            })
        
        # Name-based suggestions
        if element_name:
            suggestions.append({
                "strategy": "name",
                "locator": element_name,
                "reliability": "medium",
                "description": f"Name attribute selector"
            })
            suggestions.append({
                "strategy": "css",
                "locator": f"[name='{element_name}']",
                "reliability": "medium",
                "description": f"CSS name attribute selector"
            })
        
        # Class-based suggestions
        if element_class:
            class_names = element_class.split()
            if class_names:
                suggestions.append({
                    "strategy": "css",
                    "locator": f".{'.'.join(class_names)}",
                    "reliability": "low",
                    "description": f"CSS class selector"
                })
        
        # Type and tag combinations
        if element_tag and element_type:
            suggestions.append({
                "strategy": "css",
                "locator": f"{element_tag}[type='{element_type}']",
                "reliability": "medium",
                "description": f"CSS tag and type selector"
            })
        
        return suggestions
    
    def run_comprehensive_diagnostics(self, url: str, step_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run comprehensive diagnostics for a step replay scenario."""
        logger.info("Running comprehensive step replay diagnostics...")
        
        results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "url": url,
            "step_data": step_data,
            "diagnostics": {}
        }
        
        try:
            # Page state analysis
            results["diagnostics"]["page_state"] = self.analyze_page_state(url)
            
            # Element locator testing
            locator_strategy = step_data.get('locator_strategy', '')
            locator = step_data.get('locator', '')
            
            if locator_strategy and locator:
                results["diagnostics"]["element_testing"] = self.test_element_locators(
                    locator_strategy, locator, int(step_data.get('timeout', 10))
                )
            
            # Generate suggestions
            if results["diagnostics"]["page_state"].get("interactive_elements"):
                # Find similar elements for suggestions
                target_locator = locator.lower()
                similar_elements = []
                
                for elem in results["diagnostics"]["page_state"]["interactive_elements"]:
                    if (target_locator in elem.get('id', '').lower() or 
                        target_locator in elem.get('name', '').lower()):
                        similar_elements.append(elem)
                
                if similar_elements:
                    results["diagnostics"]["suggestions"] = []
                    for elem in similar_elements[:3]:  # Top 3 suggestions
                        suggestions = self.suggest_alternative_locators(elem)
                        results["diagnostics"]["suggestions"].extend(suggestions)
            
        except Exception as e:
            results["error"] = f"Diagnostics failed: {str(e)}"
        
        return results

def setup_webdriver():
    """Setup WebDriver for diagnostics."""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        logger.error(f"Failed to setup WebDriver: {e}")
        return None

def main():
    """Main diagnostics function."""
    parser = argparse.ArgumentParser(description="Step Replay Diagnostics Tool")
    parser.add_argument("--url", help="Website URL to analyze")
    parser.add_argument("--step-data", help="JSON string with step data")
    parser.add_argument("--interactive", action="store_true", help="Run interactive diagnostics")
    parser.add_argument("--output", help="Output file for results (JSON format)")
    
    args = parser.parse_args()
    
    if args.interactive:
        # Interactive mode
        print("=== Step Replay Diagnostics - Interactive Mode ===")
        url = input("Enter website URL: ").strip()
        locator_strategy = input("Enter locator strategy (id/name/css/xpath): ").strip()
        locator = input("Enter locator value: ").strip()
        
        step_data = {
            "action": "type",
            "locator_strategy": locator_strategy,
            "locator": locator,
            "timeout": 10
        }
    elif args.url and args.step_data:
        url = args.url
        try:
            step_data = json.loads(args.step_data)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in step-data: {e}")
            return 1
    else:
        parser.print_help()
        return 1
    
    # Setup WebDriver
    driver = setup_webdriver()
    if not driver:
        logger.error("Failed to setup WebDriver")
        return 1
    
    try:
        # Run diagnostics
        diagnostics = StepReplayDiagnostics(driver)
        results = diagnostics.run_comprehensive_diagnostics(url, step_data)
        
        # Output results
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Results saved to: {args.output}")
        else:
            print(json.dumps(results, indent=2))
        
        return 0
        
    except Exception as e:
        logger.error(f"Diagnostics failed: {e}")
        return 1
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
