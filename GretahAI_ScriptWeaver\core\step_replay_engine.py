"""
Step Replay Engine for GretahAI ScriptWeaver

This module provides functionality to replay test steps in a browser session
to reach the correct state for interactive element selection in multi-step scenarios.

Key Features:
- Executes test steps sequentially to build up browser state
- Supports various action types (navigate, click, type, select, etc.)
- Handles dynamic content loading and page transitions
- Provides error handling and fallback mechanisms
- Maintains browser session state for stateful element selection
- Integrates with existing step data structure from StateManager
- Enhanced retry mechanisms and element waiting strategies
- Graceful error recovery with alternative locator strategies
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    ElementNotInteractableException,
    StaleElementReferenceException,
    WebDriverException
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.step_replay_engine")

# Configuration constants for enhanced replay
DEFAULT_TIMEOUT = 10
RETRY_ATTEMPTS = 3
RETRY_DELAY = 1.0
PAGE_LOAD_TIMEOUT = 30
ELEMENT_INTERACTION_DELAY = 0.5
STEP_TRANSITION_DELAY = 1.0

class StepReplayEngine:
    """
    Engine for replaying test steps to build browser state for element selection.

    This class executes test steps sequentially in a browser session to reach
    the correct page state before opening the interactive element selector.

    Enhanced Features:
    - Robust element finding with multiple wait strategies
    - Retry mechanisms for failed element interactions
    - Page state verification between steps
    - Graceful error recovery with alternative locator strategies
    - Detailed logging and diagnostic information
    """

    def __init__(self, driver: webdriver.Chrome, config: Dict[str, Any] = None):
        """
        Initialize the step replay engine.

        Args:
            driver: Selenium WebDriver instance to use for step execution
            config: Optional configuration dictionary for timeouts and retry settings
        """
        self.driver = driver
        self.executed_steps = []
        self.step_context = {}
        self.last_error = None

        # Configuration with defaults
        self.config = config or {}
        self.default_timeout = self.config.get('default_timeout', DEFAULT_TIMEOUT)
        self.retry_attempts = self.config.get('retry_attempts', RETRY_ATTEMPTS)
        self.retry_delay = self.config.get('retry_delay', RETRY_DELAY)
        self.page_load_timeout = self.config.get('page_load_timeout', PAGE_LOAD_TIMEOUT)
        self.element_interaction_delay = self.config.get('element_interaction_delay', ELEMENT_INTERACTION_DELAY)
        self.step_transition_delay = self.config.get('step_transition_delay', STEP_TRANSITION_DELAY)

        # Diagnostic information
        self.step_diagnostics = []
        
    def replay_steps_to_current(self, step_table: List[Dict[str, Any]], 
                               current_step_index: int,
                               website_url: str,
                               test_data: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Replay all steps up to (but not including) the current step index.
        
        Args:
            step_table: List of test steps in automation-ready format
            current_step_index: Index of the current step (0-based)
            website_url: Base website URL for navigation
            test_data: Test data dictionary for parameter substitution
            
        Returns:
            Tuple of (success: bool, message: str)
        """
        logger.info(f"=== Starting step replay to reach step {current_step_index + 1} ===")
        
        if not step_table or current_step_index <= 0:
            logger.info("No previous steps to replay")
            return True, "No previous steps to replay"
            
        try:
            # Reset state
            self.executed_steps = []
            self.step_context = {}
            self.last_error = None
            self.step_diagnostics = []

            # Verify initial page state
            if not self._verify_page_ready():
                logger.warning("Page not ready for step replay, proceeding anyway")

            # Execute steps sequentially up to current step
            for i in range(current_step_index):
                step = step_table[i]
                step_no = step.get('step_no', i + 1)

                logger.info(f"Replaying step {step_no}: {step.get('action', 'unknown')}")

                # Record step start time for diagnostics
                step_start_time = time.time()

                success, error_msg = self._execute_single_step_with_retry(step, website_url, test_data)

                # Record step completion time
                step_duration = time.time() - step_start_time

                # Store diagnostic information
                self.step_diagnostics.append({
                    'step_no': step_no,
                    'action': step.get('action', 'unknown'),
                    'success': success,
                    'duration': step_duration,
                    'error_msg': error_msg if not success else None
                })

                if not success:
                    error_message = f"Step {step_no} failed during replay: {error_msg}"
                    logger.error(error_message)
                    self._log_diagnostic_info(step_no, step, error_msg)
                    self.last_error = error_message
                    return False, error_message

                self.executed_steps.append(step)

                # Enhanced pause between steps with page state verification
                time.sleep(self.step_transition_delay)

                # Verify page is ready for next step
                if i < current_step_index - 1:  # Don't verify after last step
                    if not self._verify_page_ready():
                        logger.warning(f"Page not ready after step {step_no}, continuing anyway")

            success_message = f"Successfully replayed {len(self.executed_steps)} steps"
            logger.info(f"=== {success_message} ===")
            self._log_replay_summary()
            return True, success_message

        except Exception as e:
            error_message = f"Unexpected error during step replay: {str(e)}"
            logger.error(error_message, exc_info=True)
            self.last_error = error_message
            return False, error_message
    
    def _execute_single_step_with_retry(self, step: Dict[str, Any],
                                      website_url: str,
                                      test_data: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Execute a single test step with retry mechanism.

        Args:
            step: Step dictionary with action, locator, etc.
            website_url: Base website URL
            test_data: Test data for parameter substitution

        Returns:
            Tuple of (success: bool, error_message: str)
        """
        last_error = None

        for attempt in range(self.retry_attempts):
            try:
                success, error_msg = self._execute_single_step(step, website_url, test_data)

                if success:
                    if attempt > 0:
                        logger.info(f"Step succeeded on attempt {attempt + 1}")
                    return True, error_msg

                last_error = error_msg

                # Don't retry navigation or wait actions
                action = step.get('action', '').lower()
                if action in ['navigate', 'wait', 'verify', 'assert']:
                    break

                if attempt < self.retry_attempts - 1:
                    logger.warning(f"Step failed on attempt {attempt + 1}: {error_msg}")
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)

            except Exception as e:
                last_error = f"Exception during step execution: {str(e)}"
                logger.error(last_error, exc_info=True)

                if attempt < self.retry_attempts - 1:
                    time.sleep(self.retry_delay)

        return False, last_error or "Step failed after all retry attempts"

    def _execute_single_step(self, step: Dict[str, Any],
                           website_url: str,
                           test_data: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Execute a single test step in the browser.

        Args:
            step: Step dictionary with action, locator, etc.
            website_url: Base website URL
            test_data: Test data for parameter substitution

        Returns:
            Tuple of (success: bool, error_message: str)
        """
        try:
            action = step.get('action', '').lower()
            locator_strategy = step.get('locator_strategy', '')
            locator = step.get('locator', '')
            test_data_param = step.get('test_data_param', '')
            timeout = int(step.get('timeout', self.default_timeout))

            # Substitute test data parameters
            if test_data_param and test_data:
                # Handle {{parameter}} format
                param_key = test_data_param.strip('{}')
                if param_key in test_data:
                    test_data_param = test_data[param_key]

            logger.debug(f"Executing action: {action}, locator: {locator_strategy}={locator}, timeout: {timeout}")

            if action == 'navigate':
                return self._execute_navigate(locator or website_url, timeout)
            elif action == 'click':
                return self._execute_click(locator_strategy, locator, timeout)
            elif action in ['type', 'enter_text', 'input']:
                return self._execute_type(locator_strategy, locator, test_data_param, timeout)
            elif action == 'select':
                return self._execute_select(locator_strategy, locator, test_data_param, timeout)
            elif action == 'wait':
                return self._execute_wait(locator_strategy, locator, timeout)
            elif action in ['verify', 'assert']:
                # For replay, we'll skip verification steps to focus on state building
                logger.debug(f"Skipping verification step during replay: {action}")
                return True, "Verification step skipped during replay"
            else:
                logger.warning(f"Unknown action type: {action}")
                return True, f"Unknown action '{action}' skipped during replay"

        except Exception as e:
            error_msg = f"Error executing step: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False, error_msg
    
    def _execute_navigate(self, url: str, timeout: int) -> Tuple[bool, str]:
        """Execute navigation action with enhanced page load verification."""
        try:
            logger.debug(f"Navigating to: {url}")
            self.driver.get(url)

            # Enhanced page load verification
            wait = WebDriverWait(self.driver, timeout)

            # Wait for body element
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            # Wait for document ready state
            wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")

            # Additional wait for dynamic content
            time.sleep(1.0)

            logger.debug(f"Page loaded successfully: {url}")
            return True, f"Successfully navigated to {url}"

        except TimeoutException:
            return False, f"Timeout waiting for page to load: {url}"
        except Exception as e:
            return False, f"Navigation failed: {str(e)}"
    
    def _execute_click(self, locator_strategy: str, locator: str, timeout: int) -> Tuple[bool, str]:
        """Execute click action with enhanced element interaction."""
        try:
            element = self._find_element_with_multiple_strategies(locator_strategy, locator, timeout)
            if not element:
                return False, f"Element not found: {locator_strategy}={locator}"

            # Ensure element is visible and interactable
            if not self._ensure_element_interactable(element):
                return False, f"Element not interactable after preparation: {locator}"

            # Scroll element into view with center alignment
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(self.element_interaction_delay)

            # Try multiple click strategies
            click_success = False
            last_error = None

            # Strategy 1: Regular click
            try:
                element.click()
                click_success = True
            except Exception as e:
                last_error = str(e)
                logger.debug(f"Regular click failed: {e}")

            # Strategy 2: JavaScript click if regular click failed
            if not click_success:
                try:
                    self.driver.execute_script("arguments[0].click();", element)
                    click_success = True
                    logger.debug("JavaScript click succeeded")
                except Exception as e:
                    last_error = str(e)
                    logger.debug(f"JavaScript click failed: {e}")

            # Strategy 3: Action chains click
            if not click_success:
                try:
                    ActionChains(self.driver).move_to_element(element).click().perform()
                    click_success = True
                    logger.debug("Action chains click succeeded")
                except Exception as e:
                    last_error = str(e)
                    logger.debug(f"Action chains click failed: {e}")

            if click_success:
                # Brief wait for any page transitions
                time.sleep(self.element_interaction_delay)
                return True, f"Successfully clicked element: {locator}"
            else:
                return False, f"All click strategies failed. Last error: {last_error}"

        except Exception as e:
            return False, f"Click failed: {str(e)}"
    
    def _execute_type(self, locator_strategy: str, locator: str, text: str, timeout: int) -> Tuple[bool, str]:
        """Execute type/input action with enhanced text input handling."""
        try:
            element = self._find_element_with_multiple_strategies(locator_strategy, locator, timeout)
            if not element:
                return False, f"Element not found: {locator_strategy}={locator}"

            # Ensure element is visible and interactable
            if not self._ensure_element_interactable(element):
                return False, f"Element not interactable after preparation: {locator}"

            # Focus on the element first
            element.click()
            time.sleep(0.1)

            # Clear existing text with multiple strategies
            try:
                element.clear()
            except Exception:
                # Fallback: select all and delete
                try:
                    element.send_keys(Keys.CONTROL + "a")
                    element.send_keys(Keys.DELETE)
                except Exception:
                    logger.debug("Could not clear element, proceeding with text input")

            # Type new text if provided
            if text:
                element.send_keys(text)
                time.sleep(self.element_interaction_delay)

            return True, f"Successfully typed text into element: {locator}"

        except ElementNotInteractableException:
            return False, f"Element not interactable: {locator}"
        except Exception as e:
            return False, f"Type action failed: {str(e)}"
    
    def _execute_select(self, locator_strategy: str, locator: str, value: str, timeout: int) -> Tuple[bool, str]:
        """Execute select dropdown action."""
        try:
            element = self._find_element(locator_strategy, locator, timeout)
            if not element:
                return False, f"Element not found: {locator_strategy}={locator}"
            
            select = Select(element)
            
            # Try different selection methods
            try:
                select.select_by_visible_text(value)
            except NoSuchElementException:
                try:
                    select.select_by_value(value)
                except NoSuchElementException:
                    select.select_by_index(int(value))
            
            return True, f"Successfully selected option: {value}"
            
        except Exception as e:
            return False, f"Select action failed: {str(e)}"
    
    def _execute_wait(self, locator_strategy: str, locator: str, timeout: int) -> Tuple[bool, str]:
        """Execute wait action."""
        try:
            if locator_strategy and locator:
                # Wait for specific element
                element = self._find_element(locator_strategy, locator, timeout)
                if element:
                    return True, f"Element appeared: {locator}"
                else:
                    return False, f"Element did not appear within timeout: {locator}"
            else:
                # Simple time-based wait
                time.sleep(min(timeout, 5))  # Cap at 5 seconds for replay
                return True, f"Waited {timeout} seconds"
                
        except Exception as e:
            return False, f"Wait action failed: {str(e)}"
    
    def _find_element_with_multiple_strategies(self, locator_strategy: str, locator: str, timeout: int):
        """Find element using multiple wait strategies and fallback approaches."""
        try:
            # Primary strategy: element is present and visible
            element = self._find_element_with_condition(locator_strategy, locator, timeout,
                                                      EC.element_to_be_clickable)
            if element:
                return element

            # Fallback 1: element is present (may not be visible yet)
            element = self._find_element_with_condition(locator_strategy, locator, timeout // 2,
                                                      EC.presence_of_element_located)
            if element:
                logger.debug(f"Found element with presence condition: {locator_strategy}={locator}")
                return element

            # Fallback 2: try alternative locator strategies if original fails
            if locator_strategy.lower() == 'id':
                # Try CSS selector as fallback
                css_locator = f"#{locator}"
                element = self._find_element_with_condition('css', css_locator, timeout // 3,
                                                          EC.presence_of_element_located)
                if element:
                    logger.debug(f"Found element with CSS fallback: {css_locator}")
                    return element

            logger.warning(f"Element not found with any strategy: {locator_strategy}={locator}")
            return None

        except Exception as e:
            logger.error(f"Error in multi-strategy element finding: {str(e)}")
            return None

    def _find_element_with_condition(self, locator_strategy: str, locator: str, timeout: int, condition):
        """Find element using the specified locator strategy and wait condition."""
        try:
            wait = WebDriverWait(self.driver, timeout)
            by_locator = self._get_by_locator(locator_strategy, locator)

            if by_locator is None:
                return None

            return wait.until(condition(by_locator))

        except TimeoutException:
            logger.debug(f"Element not found within timeout: {locator_strategy}={locator}")
            return None
        except Exception as e:
            logger.debug(f"Error finding element: {str(e)}")
            return None

    def _get_by_locator(self, locator_strategy: str, locator: str):
        """Convert locator strategy and value to Selenium By locator."""
        strategy = locator_strategy.lower()

        if strategy == 'id':
            return (By.ID, locator)
        elif strategy == 'name':
            return (By.NAME, locator)
        elif strategy in ['css', 'css_selector']:
            return (By.CSS_SELECTOR, locator)
        elif strategy == 'xpath':
            return (By.XPATH, locator)
        elif strategy == 'class':
            return (By.CLASS_NAME, locator)
        elif strategy == 'tag':
            return (By.TAG_NAME, locator)
        elif strategy == 'link_text':
            return (By.LINK_TEXT, locator)
        elif strategy == 'partial_link_text':
            return (By.PARTIAL_LINK_TEXT, locator)
        else:
            logger.warning(f"Unknown locator strategy: {locator_strategy}")
            return None

    def _find_element(self, locator_strategy: str, locator: str, timeout: int):
        """Legacy find element method for backward compatibility."""
        return self._find_element_with_multiple_strategies(locator_strategy, locator, timeout)

    def _ensure_element_interactable(self, element) -> bool:
        """Ensure element is ready for interaction."""
        try:
            # Check if element is displayed
            if not element.is_displayed():
                logger.debug("Element is not displayed")
                return False

            # Check if element is enabled
            if not element.is_enabled():
                logger.debug("Element is not enabled")
                return False

            # Scroll element into view if needed
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.2)

            return True

        except Exception as e:
            logger.debug(f"Error checking element interactability: {e}")
            return False

    def _verify_page_ready(self) -> bool:
        """Verify that the page is ready for interaction."""
        try:
            # Check document ready state
            ready_state = self.driver.execute_script("return document.readyState")
            if ready_state != "complete":
                logger.debug(f"Document not ready: {ready_state}")
                return False

            # Check for jQuery if present
            try:
                jquery_ready = self.driver.execute_script("return typeof jQuery !== 'undefined' ? jQuery.active === 0 : true")
                if not jquery_ready:
                    logger.debug("jQuery requests still active")
                    return False
            except Exception:
                # jQuery not present, continue
                pass

            # Wait for any pending animations or transitions
            time.sleep(0.5)

            return True

        except Exception as e:
            logger.debug(f"Error verifying page readiness: {e}")
            return False

    def _log_diagnostic_info(self, step_no: int, step: Dict[str, Any], error_msg: str):
        """Log detailed diagnostic information for failed steps."""
        try:
            logger.error(f"=== STEP {step_no} FAILURE DIAGNOSTICS ===")
            logger.error(f"Action: {step.get('action', 'unknown')}")
            logger.error(f"Locator Strategy: {step.get('locator_strategy', 'none')}")
            logger.error(f"Locator: {step.get('locator', 'none')}")
            logger.error(f"Error: {error_msg}")

            # Log current page information
            try:
                current_url = self.driver.current_url
                page_title = self.driver.title
                logger.error(f"Current URL: {current_url}")
                logger.error(f"Page Title: {page_title}")
            except Exception:
                logger.error("Could not retrieve page information")

            # Log available elements with similar locators
            locator_strategy = step.get('locator_strategy', '').lower()
            locator = step.get('locator', '')

            if locator_strategy == 'id' and locator:
                try:
                    # Find elements with similar IDs
                    similar_elements = self.driver.find_elements(By.CSS_SELECTOR, f"[id*='{locator}']")
                    if similar_elements:
                        logger.error(f"Found {len(similar_elements)} elements with similar ID containing '{locator}'")
                        for i, elem in enumerate(similar_elements[:5]):  # Log first 5
                            try:
                                elem_id = elem.get_attribute('id')
                                elem_tag = elem.tag_name
                                logger.error(f"  {i+1}. {elem_tag}#{elem_id}")
                            except Exception:
                                pass
                except Exception:
                    logger.error("Could not search for similar elements")

            logger.error("=== END DIAGNOSTICS ===")

        except Exception as e:
            logger.error(f"Error logging diagnostics: {e}")

    def _log_replay_summary(self):
        """Log summary of step replay execution."""
        try:
            logger.info("=== STEP REPLAY SUMMARY ===")
            logger.info(f"Total steps executed: {len(self.executed_steps)}")

            if self.step_diagnostics:
                total_duration = sum(d['duration'] for d in self.step_diagnostics)
                logger.info(f"Total execution time: {total_duration:.2f} seconds")

                for diag in self.step_diagnostics:
                    status = "✓" if diag['success'] else "✗"
                    logger.info(f"  Step {diag['step_no']}: {status} {diag['action']} ({diag['duration']:.2f}s)")

            logger.info("=== END SUMMARY ===")

        except Exception as e:
            logger.error(f"Error logging replay summary: {e}")
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get summary of executed steps."""
        return {
            'executed_steps_count': len(self.executed_steps),
            'executed_steps': [step.get('step_no', i+1) for i, step in enumerate(self.executed_steps)],
            'last_error': self.last_error,
            'current_url': self.driver.current_url if self.driver else None
        }
