#!/usr/bin/env python3
"""
Regression Test Framework Validation Script

This script validates that the comprehensive regression testing framework
is properly set up and all components are working correctly.
"""

import sys
import os
import logging

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("framework_validation")

def validate_imports():
    """Validate that all test modules can be imported."""
    logger.info("Validating test module imports...")
    
    test_modules = [
        'tests.regression.test_end_to_end_regression',
        'tests.regression.test_cross_browser_compatibility',
        'tests.regression.test_performance_regression',
        'tests.regression.test_error_scenario_regression',
        'tests.regression.test_integration_regression',
        'tests.regression.test_data_validation_regression',
        'tests.regression.run_regression_tests'
    ]
    
    success_count = 0
    
    for module in test_modules:
        try:
            __import__(module)
            logger.info(f"✅ {module}")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ {module}: {e}")
    
    logger.info(f"Import validation: {success_count}/{len(test_modules)} modules imported successfully")
    return success_count == len(test_modules)

def validate_core_components():
    """Validate core script-based state building components."""
    logger.info("Validating core components...")
    
    try:
        from core.script_based_state_builder import ScriptBasedStateBuilder
        logger.info("✅ ScriptBasedStateBuilder imported")
        
        # Test basic initialization
        state_builder = ScriptBasedStateBuilder()
        logger.info("✅ ScriptBasedStateBuilder initialized")
        
        # Test configuration
        config = {'test': 'value'}
        state_builder_with_config = ScriptBasedStateBuilder(config=config)
        assert state_builder_with_config.config == config
        logger.info("✅ ScriptBasedStateBuilder configuration works")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Core component validation failed: {e}")
        return False

def validate_test_runner():
    """Validate the regression test runner."""
    logger.info("Validating test runner...")
    
    try:
        from tests.regression.run_regression_tests import RegressionTestRunner
        logger.info("✅ RegressionTestRunner imported")
        
        # Test initialization
        runner = RegressionTestRunner()
        logger.info("✅ RegressionTestRunner initialized")
        
        # Test configuration
        assert len(runner.test_suites) > 0
        logger.info(f"✅ Test runner has {len(runner.test_suites)} test suites configured")
        
        # Validate test suite configuration
        required_fields = ['name', 'module', 'description', 'timeout', 'critical']
        for suite in runner.test_suites:
            for field in required_fields:
                assert field in suite, f"Missing field '{field}' in test suite: {suite}"
        
        logger.info("✅ All test suites properly configured")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test runner validation failed: {e}")
        return False

def validate_test_files():
    """Validate that all test files exist and are accessible."""
    logger.info("Validating test files...")
    
    test_dir = os.path.dirname(__file__)
    expected_files = [
        'test_end_to_end_regression.py',
        'test_cross_browser_compatibility.py',
        'test_performance_regression.py',
        'test_error_scenario_regression.py',
        'test_integration_regression.py',
        'test_data_validation_regression.py',
        'run_regression_tests.py',
        'README.md'
    ]
    
    missing_files = []
    
    for filename in expected_files:
        filepath = os.path.join(test_dir, filename)
        if os.path.exists(filepath):
            logger.info(f"✅ {filename}")
        else:
            logger.error(f"❌ {filename} - File not found")
            missing_files.append(filename)
    
    if missing_files:
        logger.error(f"Missing files: {missing_files}")
        return False
    
    logger.info("✅ All test files present")
    return True

def validate_dependencies():
    """Validate required dependencies are available."""
    logger.info("Validating dependencies...")
    
    required_packages = [
        'pytest',
        'psutil',
        'selenium'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package}")
        except ImportError:
            logger.error(f"❌ {package} - Package not found")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing packages: {missing_packages}")
        logger.info("Install missing packages with: pip install pytest psutil selenium")
        return False
    
    logger.info("✅ All required dependencies available")
    return True

def validate_sample_test_execution():
    """Validate that a sample test can be executed."""
    logger.info("Validating sample test execution...")
    
    try:
        from tests.regression.test_data_validation_regression import TestDataValidationRegression
        
        # Create test instance
        test_instance = TestDataValidationRegression()
        logger.info("✅ Test instance created")
        
        # Test a simple validation method
        test_instance.test_empty_and_null_data_handling()
        logger.info("✅ Sample test executed successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Sample test execution failed: {e}")
        return False

def main():
    """Main validation function."""
    logger.info("=" * 60)
    logger.info("REGRESSION TEST FRAMEWORK VALIDATION")
    logger.info("=" * 60)
    
    validations = [
        ("Dependencies", validate_dependencies),
        ("Test Files", validate_test_files),
        ("Module Imports", validate_imports),
        ("Core Components", validate_core_components),
        ("Test Runner", validate_test_runner),
        ("Sample Test Execution", validate_sample_test_execution)
    ]
    
    results = {}
    
    for name, validation_func in validations:
        logger.info(f"\n--- {name} ---")
        try:
            results[name] = validation_func()
        except Exception as e:
            logger.error(f"Validation '{name}' failed with exception: {e}")
            results[name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("VALIDATION SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} validations passed")
    
    if passed == total:
        logger.info("🎉 All validations PASSED! Regression test framework is ready.")
        return 0
    else:
        logger.error(f"💥 {total - passed} validation(s) FAILED!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
