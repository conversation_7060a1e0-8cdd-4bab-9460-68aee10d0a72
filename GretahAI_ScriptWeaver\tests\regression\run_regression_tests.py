#!/usr/bin/env python3
"""
Comprehensive Regression Test Runner for Script-Based State Building

This script runs all regression test suites for the script-based state building
functionality and provides comprehensive reporting and analysis.

Features:
- Automated execution of all regression test suites
- Performance benchmarking and comparison
- Detailed reporting with pass/fail analysis
- HTML report generation
- CI/CD integration support
- Regression detection and alerting
"""

import sys
import os
import time
import json
import subprocess
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import argparse

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("regression_runner")

class RegressionTestRunner:
    """Comprehensive regression test runner for script-based state building."""
    
    def __init__(self, output_dir: str = None):
        """Initialize the regression test runner."""
        self.output_dir = output_dir or os.path.join(os.path.dirname(__file__), 'results')
        self.test_suites = [
            {
                'name': 'End-to-End Regression',
                'module': 'test_end_to_end_regression.py',
                'description': 'Complete workflow validation and integration testing',
                'timeout': 300,
                'critical': True
            },
            {
                'name': 'Cross-Browser Compatibility',
                'module': 'test_cross_browser_compatibility.py',
                'description': 'Browser compatibility and WebDriver testing',
                'timeout': 600,
                'critical': False
            },
            {
                'name': 'Performance Regression',
                'module': 'test_performance_regression.py',
                'description': 'Performance benchmarking and scaling tests',
                'timeout': 900,
                'critical': True
            },
            {
                'name': 'Error Scenario Regression',
                'module': 'test_error_scenario_regression.py',
                'description': 'Error handling and fallback mechanism testing',
                'timeout': 300,
                'critical': True
            },
            {
                'name': 'Integration Regression',
                'module': 'test_integration_regression.py',
                'description': 'Component integration and workflow testing',
                'timeout': 300,
                'critical': True
            },
            {
                'name': 'Data Validation Regression',
                'module': 'test_data_validation_regression.py',
                'description': 'Data format validation and edge case testing',
                'timeout': 300,
                'critical': True
            }
        ]
        
        self.results = {}
        self.start_time = None
        self.end_time = None
        
        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
    
    def run_all_tests(self, parallel: bool = False, verbose: bool = True) -> Dict[str, Any]:
        """Run all regression test suites."""
        logger.info("Starting comprehensive regression test execution")
        self.start_time = time.time()
        
        # Create test execution summary
        summary = {
            'start_time': datetime.now().isoformat(),
            'test_suites': [],
            'overall_status': 'RUNNING',
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0,
            'execution_time': 0,
            'critical_failures': []
        }
        
        if parallel:
            logger.info("Running tests in parallel mode")
            results = self._run_tests_parallel()
        else:
            logger.info("Running tests sequentially")
            results = self._run_tests_sequential(verbose)
        
        self.end_time = time.time()
        
        # Process results
        summary['execution_time'] = self.end_time - self.start_time
        summary['end_time'] = datetime.now().isoformat()
        
        for suite_name, result in results.items():
            suite_summary = {
                'name': suite_name,
                'status': result.get('status', 'UNKNOWN'),
                'execution_time': result.get('execution_time', 0),
                'tests_run': result.get('tests_run', 0),
                'failures': result.get('failures', 0),
                'errors': result.get('errors', 0),
                'skipped': result.get('skipped', 0),
                'output': result.get('output', ''),
                'critical': result.get('critical', False)
            }
            
            summary['test_suites'].append(suite_summary)
            summary['total_tests'] += suite_summary['tests_run']
            summary['failed_tests'] += suite_summary['failures'] + suite_summary['errors']
            summary['skipped_tests'] += suite_summary['skipped']
            
            # Track critical failures
            if suite_summary['critical'] and suite_summary['status'] != 'PASSED':
                summary['critical_failures'].append(suite_name)
        
        summary['passed_tests'] = summary['total_tests'] - summary['failed_tests'] - summary['skipped_tests']
        
        # Determine overall status
        if summary['critical_failures']:
            summary['overall_status'] = 'CRITICAL_FAILURE'
        elif summary['failed_tests'] > 0:
            summary['overall_status'] = 'FAILURE'
        elif summary['skipped_tests'] > 0:
            summary['overall_status'] = 'PARTIAL_SUCCESS'
        else:
            summary['overall_status'] = 'SUCCESS'
        
        # Save results
        self._save_results(summary)
        self._generate_reports(summary)
        
        return summary
    
    def _run_tests_sequential(self, verbose: bool = True) -> Dict[str, Any]:
        """Run test suites sequentially."""
        results = {}
        
        for suite in self.test_suites:
            logger.info(f"Running {suite['name']}...")
            
            start_time = time.time()
            result = self._run_single_test_suite(suite, verbose)
            execution_time = time.time() - start_time
            
            result['execution_time'] = execution_time
            result['critical'] = suite['critical']
            results[suite['name']] = result
            
            # Log immediate results
            status = result.get('status', 'UNKNOWN')
            logger.info(f"{suite['name']}: {status} ({execution_time:.2f}s)")
            
            if verbose and result.get('output'):
                logger.info(f"Output preview: {result['output'][:200]}...")
        
        return results
    
    def _run_tests_parallel(self) -> Dict[str, Any]:
        """Run test suites in parallel (simplified implementation)."""
        import concurrent.futures
        
        results = {}
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            # Submit all test suites
            future_to_suite = {
                executor.submit(self._run_single_test_suite, suite, False): suite
                for suite in self.test_suites
            }
            
            # Collect results
            for future in concurrent.futures.as_completed(future_to_suite):
                suite = future_to_suite[future]
                try:
                    result = future.result()
                    result['critical'] = suite['critical']
                    results[suite['name']] = result
                    logger.info(f"{suite['name']}: {result.get('status', 'UNKNOWN')}")
                except Exception as e:
                    logger.error(f"Error running {suite['name']}: {e}")
                    results[suite['name']] = {
                        'status': 'ERROR',
                        'error': str(e),
                        'critical': suite['critical']
                    }
        
        return results
    
    def _run_single_test_suite(self, suite: Dict[str, Any], verbose: bool = True) -> Dict[str, Any]:
        """Run a single test suite."""
        try:
            # Build pytest command
            test_file = os.path.join(os.path.dirname(__file__), suite['module'])
            
            if not os.path.exists(test_file):
                return {
                    'status': 'SKIPPED',
                    'error': f"Test file not found: {test_file}",
                    'tests_run': 0,
                    'failures': 0,
                    'errors': 0,
                    'skipped': 1
                }
            
            cmd = [
                'pytest',
                test_file,
                '-v',
                '--tb=short',
                '--disable-warnings',
                f'--timeout={suite["timeout"]}',
                '--json-report',
                f'--json-report-file={os.path.join(self.output_dir, f"{suite["name"].replace(" ", "_").lower()}_report.json")}'
            ]
            
            # Run the test
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=suite['timeout'] + 60  # Add buffer to pytest timeout
            )
            
            # Parse results
            return self._parse_pytest_result(result, suite)
            
        except subprocess.TimeoutExpired:
            return {
                'status': 'TIMEOUT',
                'error': f"Test suite timed out after {suite['timeout']} seconds",
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
                'skipped': 0
            }
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
                'skipped': 0
            }
    
    def _parse_pytest_result(self, result: subprocess.CompletedProcess, suite: Dict[str, Any]) -> Dict[str, Any]:
        """Parse pytest execution result."""
        output = result.stdout + result.stderr
        
        # Try to parse JSON report if available
        json_report_file = os.path.join(self.output_dir, f"{suite['name'].replace(' ', '_').lower()}_report.json")
        
        if os.path.exists(json_report_file):
            try:
                with open(json_report_file, 'r') as f:
                    json_data = json.load(f)
                
                summary = json_data.get('summary', {})
                return {
                    'status': 'PASSED' if result.returncode == 0 else 'FAILED',
                    'tests_run': summary.get('total', 0),
                    'failures': summary.get('failed', 0),
                    'errors': summary.get('error', 0),
                    'skipped': summary.get('skipped', 0),
                    'output': output,
                    'json_report': json_data
                }
            except Exception as e:
                logger.warning(f"Could not parse JSON report for {suite['name']}: {e}")
        
        # Fallback to parsing stdout
        lines = output.split('\n')
        summary_line = None
        
        for line in lines:
            if 'failed' in line.lower() or 'passed' in line.lower() or 'error' in line.lower():
                if any(word in line for word in ['test', 'failed', 'passed', 'error']):
                    summary_line = line
                    break
        
        # Basic parsing
        tests_run = 0
        failures = 0
        errors = 0
        skipped = 0
        
        if summary_line:
            # Simple regex-like parsing
            import re
            
            passed_match = re.search(r'(\d+)\s+passed', summary_line)
            failed_match = re.search(r'(\d+)\s+failed', summary_line)
            error_match = re.search(r'(\d+)\s+error', summary_line)
            skipped_match = re.search(r'(\d+)\s+skipped', summary_line)
            
            if passed_match:
                tests_run += int(passed_match.group(1))
            if failed_match:
                failures = int(failed_match.group(1))
                tests_run += failures
            if error_match:
                errors = int(error_match.group(1))
                tests_run += errors
            if skipped_match:
                skipped = int(skipped_match.group(1))
        
        return {
            'status': 'PASSED' if result.returncode == 0 else 'FAILED',
            'tests_run': tests_run,
            'failures': failures,
            'errors': errors,
            'skipped': skipped,
            'output': output
        }
    
    def _save_results(self, summary: Dict[str, Any]):
        """Save test results to files."""
        # Save JSON summary
        json_file = os.path.join(self.output_dir, 'regression_test_summary.json')
        with open(json_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"Test summary saved to: {json_file}")
    
    def _generate_reports(self, summary: Dict[str, Any]):
        """Generate human-readable reports."""
        # Generate text report
        text_report = self._generate_text_report(summary)
        text_file = os.path.join(self.output_dir, 'regression_test_report.txt')
        with open(text_file, 'w') as f:
            f.write(text_report)
        
        # Generate HTML report
        html_report = self._generate_html_report(summary)
        html_file = os.path.join(self.output_dir, 'regression_test_report.html')
        with open(html_file, 'w') as f:
            f.write(html_report)
        
        logger.info(f"Reports generated: {text_file}, {html_file}")
    
    def _generate_text_report(self, summary: Dict[str, Any]) -> str:
        """Generate text report."""
        report = []
        report.append("=" * 80)
        report.append("GRETAH AI SCRIPTWEAVER - SCRIPT-BASED STATE BUILDING")
        report.append("COMPREHENSIVE REGRESSION TEST REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Summary
        report.append(f"Overall Status: {summary['overall_status']}")
        report.append(f"Execution Time: {summary['execution_time']:.2f} seconds")
        report.append(f"Total Tests: {summary['total_tests']}")
        report.append(f"Passed: {summary['passed_tests']}")
        report.append(f"Failed: {summary['failed_tests']}")
        report.append(f"Skipped: {summary['skipped_tests']}")
        report.append("")
        
        # Critical failures
        if summary['critical_failures']:
            report.append("CRITICAL FAILURES:")
            for failure in summary['critical_failures']:
                report.append(f"  ❌ {failure}")
            report.append("")
        
        # Test suite details
        report.append("TEST SUITE DETAILS:")
        report.append("-" * 40)
        
        for suite in summary['test_suites']:
            status_icon = "✅" if suite['status'] == 'PASSED' else "❌"
            critical_marker = " [CRITICAL]" if suite['critical'] else ""
            
            report.append(f"{status_icon} {suite['name']}{critical_marker}")
            report.append(f"   Status: {suite['status']}")
            report.append(f"   Tests: {suite['tests_run']}, Failures: {suite['failures']}, Errors: {suite['errors']}")
            report.append(f"   Time: {suite['execution_time']:.2f}s")
            report.append("")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def _generate_html_report(self, summary: Dict[str, Any]) -> str:
        """Generate HTML report."""
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>GretahAI ScriptWeaver Regression Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .test-suite {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .passed {{ background-color: #d4edda; }}
        .failed {{ background-color: #f8d7da; }}
        .critical {{ border-left: 5px solid #dc3545; }}
        .status {{ font-weight: bold; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>GretahAI ScriptWeaver - Script-Based State Building</h1>
        <h2>Comprehensive Regression Test Report</h2>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <h3>Summary</h3>
        <p><strong>Overall Status:</strong> <span class="status">{summary['overall_status']}</span></p>
        <p><strong>Execution Time:</strong> {summary['execution_time']:.2f} seconds</p>
        <p><strong>Total Tests:</strong> {summary['total_tests']}</p>
        <p><strong>Passed:</strong> {summary['passed_tests']}</p>
        <p><strong>Failed:</strong> {summary['failed_tests']}</p>
        <p><strong>Skipped:</strong> {summary['skipped_tests']}</p>
    </div>
    
    <div class="test-suites">
        <h3>Test Suite Results</h3>
"""
        
        for suite in summary['test_suites']:
            status_class = 'passed' if suite['status'] == 'PASSED' else 'failed'
            critical_class = 'critical' if suite['critical'] else ''
            
            html += f"""
        <div class="test-suite {status_class} {critical_class}">
            <h4>{suite['name']} {'[CRITICAL]' if suite['critical'] else ''}</h4>
            <p><strong>Status:</strong> {suite['status']}</p>
            <p><strong>Tests:</strong> {suite['tests_run']}, <strong>Failures:</strong> {suite['failures']}, <strong>Errors:</strong> {suite['errors']}</p>
            <p><strong>Execution Time:</strong> {suite['execution_time']:.2f}s</p>
        </div>
"""
        
        html += """
    </div>
</body>
</html>
"""
        
        return html

def main():
    """Main function for command-line execution."""
    parser = argparse.ArgumentParser(description='Run comprehensive regression tests for script-based state building')
    parser.add_argument('--parallel', action='store_true', help='Run tests in parallel')
    parser.add_argument('--output-dir', help='Output directory for results')
    parser.add_argument('--verbose', action='store_true', default=True, help='Verbose output')
    parser.add_argument('--quiet', action='store_true', help='Quiet mode (overrides verbose)')
    
    args = parser.parse_args()
    
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    # Initialize and run tests
    runner = RegressionTestRunner(output_dir=args.output_dir)
    summary = runner.run_all_tests(parallel=args.parallel, verbose=args.verbose and not args.quiet)
    
    # Print summary
    print("\n" + "="*80)
    print("REGRESSION TEST EXECUTION COMPLETE")
    print("="*80)
    print(f"Overall Status: {summary['overall_status']}")
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['passed_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Execution Time: {summary['execution_time']:.2f} seconds")
    
    if summary['critical_failures']:
        print(f"\nCRITICAL FAILURES: {', '.join(summary['critical_failures'])}")
    
    print(f"\nDetailed reports available in: {runner.output_dir}")
    
    # Exit with appropriate code
    if summary['overall_status'] in ['CRITICAL_FAILURE', 'FAILURE']:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()
