{"start_time": "2025-06-05T23:35:33.015847", "test_suites": [{"name": "End-to-End Regression", "status": "FAILED", "execution_time": 3.9556965827941895, "tests_run": 0, "failures": 0, "errors": 0, "skipped": 0, "output": "ERROR: usage: pytest [options] [file_or_dir] [file_or_dir] [...]\npytest: error: unrecognized arguments: --timeout=300 --json-report --json-report-file=C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\tests\\regression\\results\\end-to-end_regression_report.json\n  inifile: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\pytest.ini\n  rootdir: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\n\n", "critical": true}, {"name": "Cross-Browser Compatibility", "status": "FAILED", "execution_time": 2.4945642948150635, "tests_run": 0, "failures": 0, "errors": 0, "skipped": 0, "output": "ERROR: usage: pytest [options] [file_or_dir] [file_or_dir] [...]\npytest: error: unrecognized arguments: --timeout=600 --json-report --json-report-file=C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\tests\\regression\\results\\cross-browser_compatibility_report.json\n  inifile: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\pytest.ini\n  rootdir: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\n\n", "critical": false}, {"name": "Performance Regression", "status": "FAILED", "execution_time": 2.0715694427490234, "tests_run": 0, "failures": 0, "errors": 0, "skipped": 0, "output": "ERROR: usage: pytest [options] [file_or_dir] [file_or_dir] [...]\npytest: error: unrecognized arguments: --timeout=900 --json-report --json-report-file=C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\tests\\regression\\results\\performance_regression_report.json\n  inifile: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\pytest.ini\n  rootdir: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\n\n", "critical": true}, {"name": "<PERSON><PERSON><PERSON>", "status": "FAILED", "execution_time": 1.7667498588562012, "tests_run": 0, "failures": 0, "errors": 0, "skipped": 0, "output": "ERROR: usage: pytest [options] [file_or_dir] [file_or_dir] [...]\npytest: error: unrecognized arguments: --timeout=300 --json-report --json-report-file=C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\tests\\regression\\results\\error_scenario_regression_report.json\n  inifile: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\pytest.ini\n  rootdir: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\n\n", "critical": true}, {"name": "Integration Regression", "status": "FAILED", "execution_time": 2.0401265621185303, "tests_run": 0, "failures": 0, "errors": 0, "skipped": 0, "output": "ERROR: usage: pytest [options] [file_or_dir] [file_or_dir] [...]\npytest: error: unrecognized arguments: --timeout=300 --json-report --json-report-file=C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\tests\\regression\\results\\integration_regression_report.json\n  inifile: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\pytest.ini\n  rootdir: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\n\n", "critical": true}, {"name": "Data Validation Regression", "status": "FAILED", "execution_time": 1.686110496520996, "tests_run": 0, "failures": 0, "errors": 0, "skipped": 0, "output": "ERROR: usage: pytest [options] [file_or_dir] [file_or_dir] [...]\npytest: error: unrecognized arguments: --timeout=300 --json-report --json-report-file=C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\tests\\regression\\results\\data_validation_regression_report.json\n  inifile: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\\pytest.ini\n  rootdir: C:\\GenAIJira\\JiraOllamaPython\\GRETAH-CaseForge\\GretahAI_ScriptWeaver\n\n", "critical": true}], "overall_status": "CRITICAL_FAILURE", "total_tests": 0, "passed_tests": 0, "failed_tests": 0, "skipped_tests": 0, "execution_time": 14.014817237854004, "critical_failures": ["End-to-End Regression", "Performance Regression", "<PERSON><PERSON><PERSON>", "Integration Regression", "Data Validation Regression"], "end_time": "2025-06-05T23:35:47.030665"}