#!/usr/bin/env python3
"""
Data Validation Regression Tests for Script-Based State Building

This module provides comprehensive data validation regression testing to ensure
robust handling of various data formats, edge cases, and validation scenarios
in the script-based state building functionality.

Test Coverage:
- Various step table formats and edge cases
- Test data parameter substitution validation
- Empty, null, and malformed test data handling
- Script cleanup and temporary file management
- Data type validation and conversion
"""

import sys
import os
import pytest
import logging
import tempfile
import json
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("regression.data_validation")

class TestDataValidationRegression:
    """Data validation regression tests for script-based state building."""
    
    def test_step_table_format_variations(self):
        """Test various step table format variations."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test different step table formats
        format_variations = [
            {
                'name': 'minimal_required_fields',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'should_succeed': True
            },
            {
                'name': 'all_fields_present',
                'step_table': [{
                    'step_no': '1',
                    'step_type': 'ui',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post',
                    'test_data_param': '',
                    'expected_result': 'Page loads',
                    'assertion_type': 'page_title',
                    'condition': '',
                    'timeout': 15,
                    'step_description': 'Navigate to test page'
                }],
                'should_succeed': True
            },
            {
                'name': 'extra_fields',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post',
                    'extra_field_1': 'extra_value_1',
                    'extra_field_2': 'extra_value_2',
                    'custom_attribute': 'custom_value'
                }],
                'should_succeed': True
            },
            {
                'name': 'numeric_step_no',
                'step_table': [{
                    'step_no': 1,  # Numeric instead of string
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'should_succeed': True
            },
            {
                'name': 'missing_optional_fields',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                    # Missing timeout, test_data_param, etc.
                }],
                'should_succeed': True
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for variation in format_variations:
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=variation['step_table'],
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"DATA_FORMAT_{variation['name'].upper()}"
            )
            
            if variation['should_succeed']:
                assert success, f"Format variation '{variation['name']}' should succeed: {message}"
            else:
                assert not success, f"Format variation '{variation['name']}' should fail: {message}"
            
            # Cleanup
            state_builder.cleanup()
    
    def test_test_data_parameter_substitution(self):
        """Test test data parameter substitution in various formats."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test different parameter substitution scenarios
        substitution_scenarios = [
            {
                'name': 'simple_substitution',
                'step_table': [{
                    'step_no': '1',
                    'action': 'type',
                    'locator_strategy': 'name',
                    'locator': 'username',
                    'test_data_param': 'testuser',
                    'timeout': 10
                }],
                'test_data': {'username': 'testuser'},
                'should_succeed': True
            },
            {
                'name': 'bracketed_parameters',
                'step_table': [{
                    'step_no': '1',
                    'action': 'type',
                    'locator_strategy': 'name',
                    'locator': 'username',
                    'test_data_param': '{{username}}',
                    'timeout': 10
                }],
                'test_data': {'username': 'bracketed_user'},
                'should_succeed': True
            },
            {
                'name': 'missing_parameter',
                'step_table': [{
                    'step_no': '1',
                    'action': 'type',
                    'locator_strategy': 'name',
                    'locator': 'username',
                    'test_data_param': '{{missing_param}}',
                    'timeout': 10
                }],
                'test_data': {'username': 'testuser'},  # missing_param not provided
                'should_succeed': True  # Should handle gracefully
            },
            {
                'name': 'special_characters',
                'step_table': [{
                    'step_no': '1',
                    'action': 'type',
                    'locator_strategy': 'name',
                    'locator': 'data',
                    'test_data_param': 'special_data',
                    'timeout': 10
                }],
                'test_data': {'special_data': 'Test\nData\tWith\rSpecial\x00Chars'},
                'should_succeed': True
            },
            {
                'name': 'unicode_data',
                'step_table': [{
                    'step_no': '1',
                    'action': 'type',
                    'locator_strategy': 'name',
                    'locator': 'unicode_field',
                    'test_data_param': 'unicode_text',
                    'timeout': 10
                }],
                'test_data': {'unicode_text': '测试数据 🚀 émojis'},
                'should_succeed': True
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for scenario in substitution_scenarios:
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=scenario['step_table'],
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data=scenario['test_data'],
                test_case_id=f"DATA_SUBSTITUTION_{scenario['name'].upper()}"
            )
            
            if scenario['should_succeed']:
                assert success, f"Substitution scenario '{scenario['name']}' should succeed: {message}"
            else:
                assert not success, f"Substitution scenario '{scenario['name']}' should fail: {message}"
            
            # Verify script content if generated
            if success and state_builder.partial_script_path:
                with open(state_builder.partial_script_path, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                
                # Check that test data is properly included
                assert 'TEST_DATA' in script_content, "Script should contain TEST_DATA section"
            
            # Cleanup
            state_builder.cleanup()
    
    def test_empty_and_null_data_handling(self):
        """Test handling of empty and null data scenarios."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test various empty/null data scenarios
        empty_data_scenarios = [
            {
                'name': 'empty_step_table',
                'step_table': [],
                'test_data': {},
                'current_step_index': 0,
                'should_succeed': True  # Should handle gracefully
            },
            {
                'name': 'none_step_table',
                'step_table': None,
                'test_data': {},
                'current_step_index': 0,
                'should_succeed': True  # Should handle gracefully
            },
            {
                'name': 'empty_test_data',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'test_data': {},
                'current_step_index': 1,
                'should_succeed': True
            },
            {
                'name': 'none_test_data',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'test_data': None,
                'current_step_index': 1,
                'should_succeed': True
            },
            {
                'name': 'empty_strings',
                'step_table': [{
                    'step_no': '',
                    'action': '',
                    'locator_strategy': '',
                    'locator': '',
                    'test_data_param': ''
                }],
                'test_data': {'': ''},
                'current_step_index': 1,
                'should_succeed': True  # Should handle gracefully
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for scenario in empty_data_scenarios:
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=scenario['step_table'],
                current_step_index=scenario['current_step_index'],
                website_url="https://httpbin.org/forms/post",
                test_data=scenario['test_data'],
                test_case_id=f"DATA_EMPTY_{scenario['name'].upper()}"
            )
            
            # All scenarios should be handled gracefully
            assert isinstance(success, bool), f"Success should be boolean for '{scenario['name']}'"
            assert isinstance(message, str), f"Message should be string for '{scenario['name']}'"
            
            if scenario['should_succeed']:
                # For empty/null scenarios, we expect graceful handling
                logger.info(f"Empty data scenario '{scenario['name']}': Success={success}, Message='{message[:50]}...'")
            
            # Cleanup
            state_builder.cleanup()
    
    def test_malformed_data_handling(self):
        """Test handling of malformed and invalid data."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test various malformed data scenarios
        malformed_scenarios = [
            {
                'name': 'invalid_json_in_test_data',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'test_data': {'invalid': object()},  # Non-serializable object
                'should_succeed': True  # Should handle gracefully
            },
            {
                'name': 'circular_reference',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'test_data': {},  # Will add circular reference
                'should_succeed': True
            },
            {
                'name': 'invalid_step_structure',
                'step_table': [
                    "invalid_step_as_string",
                    123,  # Invalid step as number
                    {'invalid': 'structure'}
                ],
                'test_data': {},
                'should_succeed': True  # Should handle gracefully
            },
            {
                'name': 'mixed_data_types',
                'step_table': [{
                    'step_no': 1.5,  # Float instead of string/int
                    'action': ['navigate'],  # List instead of string
                    'locator_strategy': {'type': 'url'},  # Dict instead of string
                    'locator': True,  # Boolean instead of string
                    'timeout': 'ten'  # String instead of number
                }],
                'test_data': {
                    'string_key': 'value',
                    'int_key': 123,
                    'float_key': 45.67,
                    'bool_key': True,
                    'list_key': [1, 2, 3],
                    'dict_key': {'nested': 'value'}
                },
                'should_succeed': True
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for scenario in malformed_scenarios:
            # Handle circular reference scenario
            if scenario['name'] == 'circular_reference':
                circular_dict = {}
                circular_dict['self'] = circular_dict
                scenario['test_data'] = circular_dict
            
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=scenario['step_table'],
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data=scenario['test_data'],
                test_case_id=f"DATA_MALFORMED_{scenario['name'].upper()}"
            )
            
            # Should handle malformed data gracefully
            assert isinstance(success, bool), f"Success should be boolean for '{scenario['name']}'"
            assert isinstance(message, str), f"Message should be string for '{scenario['name']}'"
            
            logger.info(f"Malformed data scenario '{scenario['name']}': Success={success}")
            
            # Cleanup
            state_builder.cleanup()
    
    def test_script_cleanup_and_file_management(self):
        """Test script cleanup and temporary file management."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test cleanup with different configurations
        cleanup_scenarios = [
            {
                'name': 'auto_cleanup_enabled',
                'config': {'cleanup_scripts': True},
                'should_cleanup': True
            },
            {
                'name': 'auto_cleanup_disabled',
                'config': {'cleanup_scripts': False},
                'should_cleanup': False
            },
            {
                'name': 'default_config',
                'config': {},
                'should_cleanup': True  # Default behavior
            }
        ]
        
        for scenario in cleanup_scenarios:
            state_builder = ScriptBasedStateBuilder(config=scenario['config'])
            
            # Generate and execute script
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=[{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post',
                    'timeout': 15
                }],
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"DATA_CLEANUP_{scenario['name'].upper()}"
            )
            
            # Record script path before cleanup
            script_path = state_builder.partial_script_path
            
            # Test cleanup
            state_builder.cleanup()
            
            # Verify cleanup behavior
            if script_path:
                if scenario['should_cleanup']:
                    assert not os.path.exists(script_path), \
                           f"Script should be cleaned up for scenario '{scenario['name']}'"
                else:
                    # Script might still exist if cleanup is disabled
                    # This depends on the actual implementation
                    pass
            
            logger.info(f"Cleanup scenario '{scenario['name']}': Script path={script_path}, Exists={os.path.exists(script_path) if script_path else False}")
    
    def test_data_type_validation_and_conversion(self):
        """Test data type validation and conversion."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test various data type scenarios
        type_scenarios = [
            {
                'name': 'string_timeout',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post',
                    'timeout': '15'  # String instead of int
                }],
                'should_succeed': True
            },
            {
                'name': 'float_timeout',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post',
                    'timeout': 15.5  # Float timeout
                }],
                'should_succeed': True
            },
            {
                'name': 'boolean_values',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post',
                    'enabled': True,
                    'required': False
                }],
                'should_succeed': True
            },
            {
                'name': 'none_values',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post',
                    'optional_field': None
                }],
                'should_succeed': True
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for scenario in type_scenarios:
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=scenario['step_table'],
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"DATA_TYPE_{scenario['name'].upper()}"
            )
            
            if scenario['should_succeed']:
                assert success, f"Type scenario '{scenario['name']}' should succeed: {message}"
            else:
                assert not success, f"Type scenario '{scenario['name']}' should fail: {message}"
            
            # Cleanup
            state_builder.cleanup()
    
    def test_large_data_handling(self):
        """Test handling of large data sets."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Create large step table
        large_step_table = []
        for i in range(50):  # 50 steps
            large_step_table.append({
                'step_no': str(i + 1),
                'action': 'type' if i % 2 == 0 else 'click',
                'locator_strategy': 'id',
                'locator': f'element_{i+1}',
                'test_data_param': f'data_{i+1}',
                'timeout': 10,
                'step_description': f'Step {i+1} description'
            })
        
        # Create large test data
        large_test_data = {}
        for i in range(100):  # 100 data items
            large_test_data[f'data_{i+1}'] = f'Large test data value {i+1} with some additional content to make it larger'
        
        state_builder = ScriptBasedStateBuilder()
        
        # Test with large data
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=large_step_table,
            current_step_index=10,  # Execute first 10 steps
            website_url="https://httpbin.org/forms/post",
            test_data=large_test_data,
            test_case_id="DATA_LARGE_DATASET"
        )
        
        # Should handle large data gracefully
        assert isinstance(success, bool), "Success should be boolean for large data"
        assert isinstance(message, str), "Message should be string for large data"
        
        # If successful, verify script was generated
        if success and state_builder.partial_script_path:
            assert os.path.exists(state_builder.partial_script_path), "Large data script should be generated"
            
            # Check script size is reasonable
            script_size = os.path.getsize(state_builder.partial_script_path)
            assert script_size > 0, "Script should have content"
            assert script_size < 10 * 1024 * 1024, "Script should not be excessively large (>10MB)"
        
        logger.info(f"Large data test: Success={success}, Steps={len(large_step_table)}, Data items={len(large_test_data)}")
        
        # Cleanup
        state_builder.cleanup()
    
    def test_edge_case_data_validation(self):
        """Test edge case data validation scenarios."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test various edge cases
        edge_cases = [
            {
                'name': 'zero_step_index',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'current_step_index': 0,
                'should_succeed': True
            },
            {
                'name': 'negative_step_index',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'current_step_index': -1,
                'should_succeed': True  # Should handle gracefully
            },
            {
                'name': 'step_index_exceeds_table_length',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'current_step_index': 10,  # Exceeds table length
                'should_succeed': True  # Should handle gracefully
            },
            {
                'name': 'empty_url',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'website_url': '',
                'current_step_index': 1,
                'should_succeed': True
            },
            {
                'name': 'none_url',
                'step_table': [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post'
                }],
                'website_url': None,
                'current_step_index': 1,
                'should_succeed': True
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for case in edge_cases:
            website_url = case.get('website_url', 'https://httpbin.org/forms/post')
            
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=case['step_table'],
                current_step_index=case['current_step_index'],
                website_url=website_url,
                test_data={},
                test_case_id=f"DATA_EDGE_{case['name'].upper()}"
            )
            
            # All edge cases should be handled gracefully
            assert isinstance(success, bool), f"Success should be boolean for edge case '{case['name']}'"
            assert isinstance(message, str), f"Message should be string for edge case '{case['name']}'"
            
            logger.info(f"Edge case '{case['name']}': Success={success}")
            
            # Cleanup
            state_builder.cleanup()

if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
