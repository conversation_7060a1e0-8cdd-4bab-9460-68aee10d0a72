# Enhanced Step Replay Engine

## Overview

The Enhanced Step Replay Engine provides robust functionality for replaying test steps in a browser session to reach the correct state for interactive element selection in multi-step scenarios. This addresses the core issue where elements are only available after executing previous test steps.

## Key Enhancements

### 1. Robust Element Finding Strategies

**Multiple Wait Conditions:**
- `element_to_be_clickable`: Ensures element is visible and interactable
- `visibility_of_element_located`: Waits for element to be visible
- `presence_of_element_located`: Fallback for elements that may not be immediately visible

**Alternative Locator Strategies:**
- Automatic fallback from ID to CSS selector (`#elementId`)
- Multiple click strategies (regular click, JavaScript click, ActionChains)
- Enhanced text input with multiple clearing strategies

### 2. Retry Mechanisms

**Configurable Retry Logic:**
- Default: 3 retry attempts with 1.5-second delays
- Exponential backoff for element interaction delays
- Skip retries for navigation and wait actions (non-retriable)

**Smart Retry Decisions:**
- Retry only for element interaction failures
- Skip retries for page navigation and explicit waits
- Detailed logging of retry attempts and outcomes

### 3. Enhanced Error Handling and Diagnostics

**Comprehensive Error Reporting:**
- Detailed diagnostic information for failed steps
- Current page state analysis (URL, title, ready state)
- Available elements analysis with similar locators
- Step execution timing and performance metrics

**Diagnostic Capabilities:**
- Element availability analysis before interaction
- Page readiness verification (document.readyState, jQuery.active)
- Alternative locator suggestions for failed elements
- Comprehensive logging with step-by-step execution details

### 4. Improved Timing and Synchronization

**Page State Verification:**
- Document ready state checking
- jQuery activity monitoring (if present)
- Dynamic content loading detection
- Configurable delays between step transitions

**Enhanced Navigation:**
- Extended page load timeouts
- Center-aligned element scrolling
- Improved element visibility verification
- Better handling of dynamic content

## Configuration Options

The Enhanced Step Replay Engine supports comprehensive configuration:

```python
replay_config = {
    'default_timeout': 15,          # Element finding timeout (seconds)
    'retry_attempts': 3,            # Number of retry attempts for failed steps
    'retry_delay': 1.5,             # Delay between retry attempts (seconds)
    'page_load_timeout': 30,        # Page navigation timeout (seconds)
    'element_interaction_delay': 0.5, # Delay after element interactions (seconds)
    'step_transition_delay': 1.0    # Delay between step executions (seconds)
}
```

## Usage Examples

### Basic Step Replay with Enhanced Configuration

```python
from core.step_replay_engine import StepReplayEngine

# Enhanced configuration for better reliability
config = {
    'default_timeout': 15,
    'retry_attempts': 3,
    'retry_delay': 1.5,
    'page_load_timeout': 30,
    'element_interaction_delay': 0.5,
    'step_transition_delay': 1.0
}

replay_engine = StepReplayEngine(driver, config=config)

success, message = replay_engine.replay_steps_to_current(
    step_table, current_step_index, website_url, test_data
)

if not success:
    print(f"Step replay failed: {message}")
    if replay_engine.last_error:
        print(f"Detailed error: {replay_engine.last_error}")
    
    # Access diagnostic information
    for diag in replay_engine.step_diagnostics:
        print(f"Step {diag['step_no']}: {diag['action']} - {diag['success']} ({diag['duration']:.2f}s)")
```

### Interactive Element Selection with Enhanced Step Replay

```python
from core.interactive_selector import select_element_interactively

selected_element = select_element_interactively(
    website_url,
    replay_steps=True,
    step_table=step_table,
    current_step_index=current_step_index,
    test_data=test_data,
    progress_callback=progress_callback
)
```

## Troubleshooting Common Issues

### Element Not Found Errors

**Symptoms:**
- "Element not found: id=userid" during step replay
- Step replay fails and falls back to base URL

**Solutions:**
1. **Increase Timeouts:** Adjust `default_timeout` in configuration
2. **Add Explicit Waits:** Include wait steps before problematic elements
3. **Alternative Locators:** Use CSS selectors or XPath as alternatives
4. **Page State Verification:** Ensure previous steps complete successfully

### Timing and Synchronization Issues

**Symptoms:**
- Elements found but not interactable
- Intermittent step replay failures
- Page state inconsistencies

**Solutions:**
1. **Adjust Delays:** Increase `step_transition_delay` and `element_interaction_delay`
2. **Page Load Verification:** Ensure `page_load_timeout` is sufficient
3. **Dynamic Content:** Add explicit wait steps for dynamic content loading

### Performance Optimization

**Configuration for Fast Execution:**
```python
fast_config = {
    'default_timeout': 10,
    'retry_attempts': 2,
    'retry_delay': 0.5,
    'element_interaction_delay': 0.2,
    'step_transition_delay': 0.5
}
```

**Configuration for Reliability:**
```python
reliable_config = {
    'default_timeout': 20,
    'retry_attempts': 5,
    'retry_delay': 2.0,
    'element_interaction_delay': 1.0,
    'step_transition_delay': 2.0
}
```

## Diagnostic Tools

### Step Replay Diagnostics Tool

Use the diagnostic tool to analyze step replay issues:

```bash
python tools/step_replay_diagnostics.py --url "https://example.com" --step-data '{"action":"type","locator_strategy":"id","locator":"userid","timeout":10}'
```

### Interactive Diagnostics

```bash
python tools/step_replay_diagnostics.py --interactive
```

## Error Recovery Strategies

### Graceful Degradation

1. **Element Finding Failures:**
   - Try alternative locator strategies
   - Provide detailed error diagnostics
   - Suggest manual navigation as fallback

2. **Page State Issues:**
   - Verify page readiness before proceeding
   - Log current page state for debugging
   - Continue with warnings when possible

3. **Timing Problems:**
   - Implement exponential backoff
   - Provide configurable timeout values
   - Log timing information for optimization

### User Feedback

The enhanced step replay provides comprehensive user feedback:

- **Progress Updates:** Real-time step execution progress
- **Error Details:** Specific error messages with troubleshooting guidance
- **Diagnostic Information:** Element availability and page state analysis
- **Alternative Suggestions:** Recommended fixes for common issues

## Integration with GretahAI ScriptWeaver

The Enhanced Step Replay Engine is fully integrated with GretahAI ScriptWeaver's stateful element selection:

1. **Stage 4 Integration:** Automatic configuration and execution
2. **State Management:** Centralized error tracking and progress updates
3. **UI Feedback:** Real-time progress and comprehensive error reporting
4. **Fallback Mechanisms:** Graceful degradation to manual navigation

## Best Practices

### Step Design

1. **Explicit Waits:** Include wait steps for dynamic content
2. **Robust Locators:** Use stable element identifiers (ID > Name > CSS > XPath)
3. **Error Handling:** Design steps with retry-friendly actions
4. **Page Verification:** Include verification steps for critical page states

### Configuration Tuning

1. **Environment-Specific:** Adjust timeouts based on application performance
2. **Network Conditions:** Increase timeouts for slow network environments
3. **Dynamic Content:** Add delays for applications with heavy JavaScript
4. **Testing vs Production:** Use different configurations for different environments

### Monitoring and Debugging

1. **Enable Logging:** Use detailed logging for troubleshooting
2. **Diagnostic Tools:** Leverage built-in diagnostic capabilities
3. **Performance Metrics:** Monitor step execution times
4. **Error Patterns:** Track common failure patterns for optimization

## Future Enhancements

- **Machine Learning:** Adaptive timeout and retry strategies
- **Visual Recognition:** Element finding using visual patterns
- **Network Monitoring:** Automatic adjustment based on network conditions
- **Parallel Execution:** Concurrent step replay for performance optimization
