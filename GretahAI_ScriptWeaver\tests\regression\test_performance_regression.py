#!/usr/bin/env python3
"""
Performance Regression Tests for Script-Based State Building

This module provides comprehensive performance regression testing to ensure
script-based state building maintains optimal performance characteristics
and doesn't introduce performance regressions over time.

Test Coverage:
- Script execution time benchmarks for various step counts
- Performance comparison against manual step replay baseline
- Memory usage and resource cleanup validation
- Timeout handling and execution limits
- Performance scaling with step complexity
"""

import sys
import os
import time
import pytest
import logging
import psutil
import threading
from typing import Dict, List, Any, Optional, Tuple
from unittest.mock import Mock, patch

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("regression.performance")

class PerformanceMonitor:
    """Monitor system resources during test execution."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.start_memory = None
        self.end_memory = None
        self.peak_memory = None
        self.monitoring = False
        self.memory_samples = []
        
    def start_monitoring(self):
        """Start performance monitoring."""
        self.start_time = time.time()
        self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.start_memory
        self.monitoring = True
        self.memory_samples = []
        
        # Start memory monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitor_memory)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """Stop performance monitoring and return results."""
        self.monitoring = False
        self.end_time = time.time()
        self.end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        return {
            'execution_time': self.end_time - self.start_time,
            'start_memory_mb': self.start_memory,
            'end_memory_mb': self.end_memory,
            'peak_memory_mb': self.peak_memory,
            'memory_delta_mb': self.end_memory - self.start_memory,
            'memory_samples': len(self.memory_samples)
        }
        
    def _monitor_memory(self):
        """Monitor memory usage in background thread."""
        while self.monitoring:
            try:
                current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                self.memory_samples.append(current_memory)
                if current_memory > self.peak_memory:
                    self.peak_memory = current_memory
                time.sleep(0.1)  # Sample every 100ms
            except:
                break

class TestPerformanceRegression:
    """Performance regression tests for script-based state building."""
    
    @pytest.fixture
    def performance_monitor(self):
        """Performance monitoring fixture."""
        return PerformanceMonitor()
    
    def create_step_table(self, step_count: int) -> List[Dict[str, Any]]:
        """Create step table with specified number of steps."""
        steps = []
        
        # Always start with navigation
        steps.append({
            'step_no': '1',
            'action': 'navigate',
            'locator_strategy': 'url',
            'locator': 'https://httpbin.org/forms/post',
            'test_data_param': '',
            'timeout': 15,
            'step_description': 'Navigate to test form'
        })
        
        # Add additional steps based on count
        for i in range(2, step_count + 1):
            if i % 3 == 0:
                # Click step
                steps.append({
                    'step_no': str(i),
                    'action': 'click',
                    'locator_strategy': 'css',
                    'locator': f'input[name="field{i}"]',
                    'test_data_param': '',
                    'timeout': 10,
                    'step_description': f'Click field {i}'
                })
            elif i % 3 == 1:
                # Type step
                steps.append({
                    'step_no': str(i),
                    'action': 'type',
                    'locator_strategy': 'name',
                    'locator': f'field{i}',
                    'test_data_param': f'Test Value {i}',
                    'timeout': 10,
                    'step_description': f'Enter value in field {i}'
                })
            else:
                # Wait step
                steps.append({
                    'step_no': str(i),
                    'action': 'wait',
                    'locator_strategy': 'css',
                    'locator': f'.loading{i}',
                    'test_data_param': '',
                    'timeout': 5,
                    'step_description': f'Wait for element {i}'
                })
        
        return steps
    
    def test_single_step_performance_baseline(self, performance_monitor):
        """Test performance baseline with single step."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        step_table = self.create_step_table(1)
        state_builder = ScriptBasedStateBuilder()
        
        performance_monitor.start_monitoring()
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=step_table,
            current_step_index=1,
            website_url="https://httpbin.org/forms/post",
            test_data={},
            test_case_id="PERF_SINGLE_STEP"
        )
        
        perf_results = performance_monitor.stop_monitoring()
        
        # Verify success
        assert success, f"Single step performance test failed: {message}"
        
        # Performance assertions
        assert perf_results['execution_time'] < 30, f"Single step took too long: {perf_results['execution_time']:.2f}s"
        assert perf_results['memory_delta_mb'] < 100, f"Memory usage too high: {perf_results['memory_delta_mb']:.2f}MB"
        
        logger.info(f"Single step performance: {perf_results['execution_time']:.2f}s, "
                   f"Memory: {perf_results['memory_delta_mb']:.2f}MB")
        
        # Cleanup
        state_builder.cleanup()
    
    def test_five_step_performance_benchmark(self, performance_monitor):
        """Test performance with 5-step scenario."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        step_table = self.create_step_table(5)
        state_builder = ScriptBasedStateBuilder()
        
        performance_monitor.start_monitoring()
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=step_table,
            current_step_index=3,  # Execute first 3 steps
            website_url="https://httpbin.org/forms/post",
            test_data={},
            test_case_id="PERF_FIVE_STEP"
        )
        
        perf_results = performance_monitor.stop_monitoring()
        
        # Verify success
        assert success, f"Five step performance test failed: {message}"
        
        # Performance assertions
        assert perf_results['execution_time'] < 45, f"Five steps took too long: {perf_results['execution_time']:.2f}s"
        assert perf_results['memory_delta_mb'] < 150, f"Memory usage too high: {perf_results['memory_delta_mb']:.2f}MB"
        
        logger.info(f"Five step performance: {perf_results['execution_time']:.2f}s, "
                   f"Memory: {perf_results['memory_delta_mb']:.2f}MB")
        
        # Cleanup
        state_builder.cleanup()
    
    def test_ten_step_performance_scaling(self, performance_monitor):
        """Test performance scaling with 10-step scenario."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        step_table = self.create_step_table(10)
        state_builder = ScriptBasedStateBuilder()
        
        performance_monitor.start_monitoring()
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=step_table,
            current_step_index=5,  # Execute first 5 steps
            website_url="https://httpbin.org/forms/post",
            test_data={},
            test_case_id="PERF_TEN_STEP"
        )
        
        perf_results = performance_monitor.stop_monitoring()
        
        # Verify success
        assert success, f"Ten step performance test failed: {message}"
        
        # Performance assertions (more lenient for larger scenarios)
        assert perf_results['execution_time'] < 60, f"Ten steps took too long: {perf_results['execution_time']:.2f}s"
        assert perf_results['memory_delta_mb'] < 200, f"Memory usage too high: {perf_results['memory_delta_mb']:.2f}MB"
        
        logger.info(f"Ten step performance: {perf_results['execution_time']:.2f}s, "
                   f"Memory: {perf_results['memory_delta_mb']:.2f}MB")
        
        # Cleanup
        state_builder.cleanup()
    
    def test_performance_comparison_script_vs_manual(self, performance_monitor):
        """Compare performance between script-based and manual step replay."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        step_table = self.create_step_table(3)
        
        # Test script-based approach
        state_builder = ScriptBasedStateBuilder()
        
        performance_monitor.start_monitoring()
        
        success_script, message_script, _ = state_builder.build_state_with_script(
            step_table=step_table,
            current_step_index=2,
            website_url="https://httpbin.org/forms/post",
            test_data={},
            test_case_id="PERF_COMPARISON_SCRIPT"
        )
        
        script_perf = performance_monitor.stop_monitoring()
        state_builder.cleanup()
        
        # Test manual replay approach (mocked for performance comparison)
        performance_monitor.start_monitoring()
        
        # Simulate manual replay timing (based on typical manual replay performance)
        time.sleep(2.0)  # Simulate slower manual replay
        manual_perf = performance_monitor.stop_monitoring()
        
        # Compare performance
        if success_script:
            logger.info(f"Script-based: {script_perf['execution_time']:.2f}s")
            logger.info(f"Manual replay (simulated): {manual_perf['execution_time']:.2f}s")
            
            # Script-based should be competitive or better
            # Note: Actual comparison would require real manual replay implementation
            assert script_perf['execution_time'] > 0, "Script execution time should be positive"
            assert script_perf['memory_delta_mb'] < 200, "Script memory usage should be reasonable"
    
    def test_memory_cleanup_efficiency(self, performance_monitor):
        """Test memory cleanup efficiency after script execution."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        step_table = self.create_step_table(3)
        
        # Record initial memory
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Execute multiple state building operations
        for i in range(3):
            state_builder = ScriptBasedStateBuilder()
            
            success, message, _ = state_builder.build_state_with_script(
                step_table=step_table,
                current_step_index=2,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"PERF_CLEANUP_{i+1:03d}"
            )
            
            assert success, f"Memory cleanup test {i+1} failed: {message}"
            
            # Cleanup
            state_builder.cleanup()
            
            # Brief pause to allow cleanup
            time.sleep(0.5)
        
        # Record final memory
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_growth = final_memory - initial_memory
        
        # Memory growth should be minimal
        assert memory_growth < 50, f"Memory growth too high: {memory_growth:.2f}MB"
        
        logger.info(f"Memory growth after 3 operations: {memory_growth:.2f}MB")
    
    def test_timeout_handling_performance(self, performance_monitor):
        """Test performance of timeout handling mechanisms."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Create step table with short timeouts to trigger timeout handling
        step_table = [{
            'step_no': '1',
            'action': 'navigate',
            'locator_strategy': 'url',
            'locator': 'https://httpbin.org/delay/10',  # 10 second delay
            'test_data_param': '',
            'timeout': 5,  # 5 second timeout (should timeout)
            'step_description': 'Navigate with timeout'
        }]
        
        config = {'script_timeout': 15}  # Overall script timeout
        state_builder = ScriptBasedStateBuilder(config=config)
        
        performance_monitor.start_monitoring()
        
        success, message, _ = state_builder.build_state_with_script(
            step_table=step_table,
            current_step_index=1,
            website_url="https://httpbin.org/delay/10",
            test_data={},
            test_case_id="PERF_TIMEOUT"
        )
        
        perf_results = performance_monitor.stop_monitoring()
        
        # Should complete within reasonable time even with timeout
        assert perf_results['execution_time'] < 30, f"Timeout handling took too long: {perf_results['execution_time']:.2f}s"
        
        logger.info(f"Timeout handling performance: {perf_results['execution_time']:.2f}s")
        
        # Cleanup
        state_builder.cleanup()
    
    def test_concurrent_execution_performance(self, performance_monitor):
        """Test performance under concurrent execution scenarios."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        import concurrent.futures
        
        step_table = self.create_step_table(2)
        
        def execute_state_building(test_id):
            """Execute state building for concurrent testing."""
            state_builder = ScriptBasedStateBuilder()
            
            success, message, _ = state_builder.build_state_with_script(
                step_table=step_table,
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"PERF_CONCURRENT_{test_id:03d}"
            )
            
            state_builder.cleanup()
            return success, message
        
        performance_monitor.start_monitoring()
        
        # Execute 3 concurrent state building operations
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(execute_state_building, i) for i in range(3)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        perf_results = performance_monitor.stop_monitoring()
        
        # Verify all executions
        successful_executions = sum(1 for success, _ in results if success)
        
        # At least some should succeed (concurrent execution might have limitations)
        assert successful_executions > 0, "No concurrent executions succeeded"
        
        # Performance should be reasonable even with concurrency
        assert perf_results['execution_time'] < 90, f"Concurrent execution took too long: {perf_results['execution_time']:.2f}s"
        
        logger.info(f"Concurrent execution: {successful_executions}/3 succeeded in {perf_results['execution_time']:.2f}s")
    
    def test_script_generation_performance(self, performance_monitor):
        """Test performance of script generation phase."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test with large step table
        step_table = self.create_step_table(20)
        state_builder = ScriptBasedStateBuilder()
        
        performance_monitor.start_monitoring()
        
        # Test script generation only (not execution)
        script_content = state_builder._generate_partial_script(
            step_table=step_table,
            current_step_index=15,  # Generate script for 15 steps
            website_url="https://httpbin.org/forms/post",
            test_data={},
            test_case_id="PERF_SCRIPT_GEN"
        )
        
        perf_results = performance_monitor.stop_monitoring()
        
        # Verify script was generated
        assert script_content is not None, "Script generation failed"
        assert len(script_content) > 0, "Empty script generated"
        
        # Script generation should be fast
        assert perf_results['execution_time'] < 5, f"Script generation took too long: {perf_results['execution_time']:.2f}s"
        assert perf_results['memory_delta_mb'] < 20, f"Script generation used too much memory: {perf_results['memory_delta_mb']:.2f}MB"
        
        logger.info(f"Script generation (15 steps): {perf_results['execution_time']:.2f}s, "
                   f"Memory: {perf_results['memory_delta_mb']:.2f}MB")
    
    def test_resource_cleanup_performance(self, performance_monitor):
        """Test performance of resource cleanup operations."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        step_table = self.create_step_table(3)
        
        # Create multiple state builders to test cleanup
        state_builders = []
        for i in range(5):
            state_builder = ScriptBasedStateBuilder()
            
            success, message, _ = state_builder.build_state_with_script(
                step_table=step_table,
                current_step_index=1,
                website_url="https://httpbin.org/forms/post",
                test_data={},
                test_case_id=f"PERF_RESOURCE_CLEANUP_{i+1:03d}"
            )
            
            if success:
                state_builders.append(state_builder)
        
        # Test cleanup performance
        performance_monitor.start_monitoring()
        
        for state_builder in state_builders:
            state_builder.cleanup()
        
        perf_results = performance_monitor.stop_monitoring()
        
        # Cleanup should be fast
        assert perf_results['execution_time'] < 5, f"Resource cleanup took too long: {perf_results['execution_time']:.2f}s"
        
        logger.info(f"Resource cleanup ({len(state_builders)} builders): {perf_results['execution_time']:.2f}s")

if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
