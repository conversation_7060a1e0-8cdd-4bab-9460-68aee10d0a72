# =========================================================================
# PARTIAL SCRIPT FOR STATE BUILDING - DATA_SUBSTITUTION_SIMPLE_SUBSTITUTION
# =========================================================================
# Generated: 2025-06-06 12:00:01
# Purpose: Execute steps 1-1 to build browser state
# Target Step: 2
# Steps Included: 1
# 
# This script is automatically generated for script-based state building.
# It executes the necessary steps to reach the correct browser state before
# launching the interactive element selector.
# =========================================================================

import pytest
import time
import random
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Test data for partial script execution
TEST_DATA = {
    "website_url": "https://httpbin.org/forms/post",
    "username": "testuser"
}

@pytest.mark.order(1)
def test_step1_type(browser):
    """
    Step 1: Execute type
    Action: type
    Locator: name=username
    """
    try:
        # Find and type into element
        wait = WebDriverWait(browser, 10)
        element = wait.until(EC.element_to_be_clickable((By.NAME, "username")))
        
        # Clear and type text
        element.clear()
        element.send_keys("testuser")
        
        # Brief pause for stability
        time.sleep(random.uniform(0.3, 0.7))
        
    except Exception as e:
        print(f"Exception in test_step1_type: {repr(e)}")
        # Take screenshot for debugging
        try:
            browser.save_screenshot(f"test_step1_type_failure.png")
        except:
            pass
        raise


# =========================================================================
# BROWSER SESSION PRESERVATION
# =========================================================================
# The browser session is maintained after this script execution for use
# with the interactive element selector. The browser state should reflect
# the completion of all steps included in this partial script.
# =========================================================================
