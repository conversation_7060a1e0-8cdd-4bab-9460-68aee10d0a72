#!/usr/bin/env python3
"""
Error Scenario Regression Tests for Script-Based State Building

This module provides comprehensive error scenario testing to ensure robust
error handling, graceful degradation, and proper user feedback mechanisms
in the script-based state building functionality.

Test Coverage:
- Malformed step tables and invalid locator strategies
- Network failures and page load timeouts
- Script generation failures and execution errors
- Error reporting and user feedback mechanisms
- Fallback mechanism validation
"""

import sys
import os
import pytest
import logging
import time
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("regression.error_scenarios")

class TestErrorScenarioRegression:
    """Error scenario regression tests for script-based state building."""
    
    def test_malformed_step_table_handling(self):
        """Test handling of malformed step tables."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test various malformed step tables
        malformed_tables = [
            None,  # None step table
            [],    # Empty step table
            [{}],  # Step with no required fields
            [{'invalid': 'data'}],  # Step with invalid fields
            [{'step_no': None, 'action': None}],  # Step with None values
            [{'step_no': '', 'action': '', 'locator_strategy': '', 'locator': ''}],  # Empty strings
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for i, malformed_table in enumerate(malformed_tables):
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=malformed_table,
                current_step_index=1,
                website_url="https://example.com",
                test_data={},
                test_case_id=f"ERROR_MALFORMED_{i+1:03d}"
            )
            
            # Should handle malformed data gracefully
            assert isinstance(success, bool), f"Success should be boolean for malformed table {i+1}"
            assert isinstance(message, str), f"Message should be string for malformed table {i+1}"
            assert len(message) > 0, f"Error message should not be empty for malformed table {i+1}"
            
            # Cleanup
            state_builder.cleanup()
    
    def test_invalid_locator_strategies(self):
        """Test handling of invalid locator strategies."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test invalid locator strategies
        invalid_strategies = [
            'invalid_strategy',
            'nonexistent',
            '',
            None,
            123,  # Non-string type
            'xpath_invalid',
            'css_malformed'
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for strategy in invalid_strategies:
            step_table = [{
                'step_no': '1',
                'action': 'click',
                'locator_strategy': strategy,
                'locator': 'test-element',
                'test_data_param': '',
                'timeout': 10
            }]
            
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=step_table,
                current_step_index=1,
                website_url="https://example.com",
                test_data={},
                test_case_id=f"ERROR_INVALID_STRATEGY_{str(strategy).replace(' ', '_')}"
            )
            
            # Should handle invalid strategies gracefully
            assert isinstance(success, bool), f"Success should be boolean for strategy: {strategy}"
            assert isinstance(message, str), f"Message should be string for strategy: {strategy}"
            
            # Cleanup
            state_builder.cleanup()
    
    def test_network_failure_scenarios(self):
        """Test handling of network failures and unreachable URLs."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test various network failure scenarios
        failure_scenarios = [
            {
                'url': 'https://invalid-domain-that-does-not-exist-12345.com',
                'description': 'Non-existent domain'
            },
            {
                'url': 'http://localhost:99999',
                'description': 'Invalid port'
            },
            {
                'url': 'https://httpbin.org/status/500',
                'description': 'Server error'
            },
            {
                'url': 'https://httpbin.org/delay/30',
                'description': 'Slow response'
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        for scenario in failure_scenarios:
            step_table = [{
                'step_no': '1',
                'action': 'navigate',
                'locator_strategy': 'url',
                'locator': scenario['url'],
                'test_data_param': '',
                'timeout': 10  # Short timeout to trigger failures
            }]
            
            success, message, browser_info = state_builder.build_state_with_script(
                step_table=step_table,
                current_step_index=1,
                website_url=scenario['url'],
                test_data={},
                test_case_id=f"ERROR_NETWORK_{scenario['description'].replace(' ', '_').upper()}"
            )
            
            # Should handle network failures gracefully
            assert isinstance(success, bool), f"Success should be boolean for: {scenario['description']}"
            assert isinstance(message, str), f"Message should be string for: {scenario['description']}"
            
            logger.info(f"Network scenario '{scenario['description']}': Success={success}, Message='{message[:100]}...'")
            
            # Cleanup
            state_builder.cleanup()
    
    def test_script_generation_error_handling(self):
        """Test error handling during script generation phase."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        state_builder = ScriptBasedStateBuilder()
        
        # Test with problematic step data that might cause generation errors
        problematic_steps = [
            {
                'step_no': '1',
                'action': 'type',
                'locator_strategy': 'id',
                'locator': 'test"element\'with"quotes',  # Quotes in locator
                'test_data_param': 'Test\nData\tWith\rSpecial\x00Chars',  # Special characters
                'timeout': 10
            },
            {
                'step_no': '2',
                'action': 'click',
                'locator_strategy': 'xpath',
                'locator': '//div[@class="test\'class"]',  # Complex XPath with quotes
                'test_data_param': '',
                'timeout': 10
            }
        ]
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=problematic_steps,
            current_step_index=2,
            website_url="https://example.com",
            test_data={'special': 'data\nwith\tspecial\rchars'},
            test_case_id="ERROR_SCRIPT_GENERATION"
        )
        
        # Should handle script generation errors gracefully
        assert isinstance(success, bool), "Success should be boolean"
        assert isinstance(message, str), "Message should be string"
        
        # If generation succeeded, verify script content is valid
        if success and state_builder.partial_script_path:
            assert os.path.exists(state_builder.partial_script_path), "Generated script should exist"
            
            # Check script content for basic validity
            with open(state_builder.partial_script_path, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            assert 'import pytest' in script_content, "Script should contain pytest import"
            assert 'def test_' in script_content, "Script should contain test functions"
        
        # Cleanup
        state_builder.cleanup()
    
    def test_script_execution_error_handling(self):
        """Test error handling during script execution phase."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Create step table that will likely cause execution errors
        error_prone_steps = [
            {
                'step_no': '1',
                'action': 'navigate',
                'locator_strategy': 'url',
                'locator': 'https://httpbin.org/status/404',  # 404 error
                'test_data_param': '',
                'timeout': 10
            },
            {
                'step_no': '2',
                'action': 'click',
                'locator_strategy': 'id',
                'locator': 'non-existent-element-id',  # Element that doesn't exist
                'test_data_param': '',
                'timeout': 5
            }
        ]
        
        state_builder = ScriptBasedStateBuilder()
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=error_prone_steps,
            current_step_index=2,
            website_url="https://httpbin.org/status/404",
            test_data={},
            test_case_id="ERROR_SCRIPT_EXECUTION"
        )
        
        # Should handle execution errors gracefully
        assert isinstance(success, bool), "Success should be boolean"
        assert isinstance(message, str), "Message should be string"
        
        # Check execution results for error information
        if state_builder.execution_results:
            assert 'returncode' in state_builder.execution_results
            assert 'stderr' in state_builder.execution_results
            assert 'stdout' in state_builder.execution_results
        
        # Cleanup
        state_builder.cleanup()
    
    def test_timeout_error_scenarios(self):
        """Test various timeout error scenarios."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        # Test with very short timeout to trigger timeout errors
        timeout_config = {
            'script_timeout': 5  # Very short timeout
        }
        
        state_builder = ScriptBasedStateBuilder(config=timeout_config)
        
        # Create step that might take longer than timeout
        slow_step_table = [{
            'step_no': '1',
            'action': 'navigate',
            'locator_strategy': 'url',
            'locator': 'https://httpbin.org/delay/10',  # 10 second delay
            'test_data_param': '',
            'timeout': 15
        }]
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=slow_step_table,
            current_step_index=1,
            website_url="https://httpbin.org/delay/10",
            test_data={},
            test_case_id="ERROR_TIMEOUT"
        )
        
        # Should handle timeout gracefully
        assert isinstance(success, bool), "Success should be boolean"
        assert isinstance(message, str), "Message should be string"
        
        # If timeout occurred, message should indicate timeout
        if not success:
            assert any(keyword in message.lower() for keyword in ['timeout', 'time', 'expired']), \
                   f"Timeout error message should mention timeout: {message}"
        
        # Cleanup
        state_builder.cleanup()
    
    def test_fallback_mechanism_validation(self):
        """Test fallback mechanism from script-based to manual replay."""
        from core.interactive_selector import select_element_interactively
        
        # Create scenario that should trigger fallback
        problematic_step_table = [
            {
                'step_no': '1',
                'action': 'navigate',
                'locator_strategy': 'url',
                'locator': 'https://invalid-domain-fallback-test.com',
                'test_data_param': '',
                'timeout': 5
            }
        ]
        
        # Mock the manual replay fallback
        with patch('core.interactive_selector._manual_step_replay_fallback') as mock_fallback:
            mock_fallback.return_value = (True, "Manual replay succeeded")
            
            with patch('core.interactive_selector.launch_interactive_selector') as mock_launch:
                mock_launch.return_value = {'test': 'element'}
                
                # Test fallback mechanism
                result = select_element_interactively(
                    url="https://invalid-domain-fallback-test.com",
                    replay_steps=True,
                    step_table=problematic_step_table,
                    current_step_index=1,
                    test_data={},
                    use_script_based_state_building=True,
                    test_case_id="ERROR_FALLBACK_TEST"
                )
                
                # Should complete successfully with fallback
                assert result is not None, "Fallback should provide result"
                
                # Verify fallback was called
                mock_fallback.assert_called_once()
    
    def test_error_reporting_mechanisms(self):
        """Test error reporting and user feedback mechanisms."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        state_builder = ScriptBasedStateBuilder()
        
        # Test with invalid data to trigger error reporting
        invalid_step_table = [
            {
                'step_no': '1',
                'action': 'invalid_action_type',
                'locator_strategy': 'invalid_strategy',
                'locator': '',
                'test_data_param': '',
                'timeout': -1  # Invalid timeout
            }
        ]
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=invalid_step_table,
            current_step_index=1,
            website_url="https://example.com",
            test_data={},
            test_case_id="ERROR_REPORTING_TEST"
        )
        
        # Verify error reporting
        assert isinstance(success, bool), "Success should be boolean"
        assert isinstance(message, str), "Message should be string"
        assert len(message) > 0, "Error message should not be empty"
        
        # Check if last_error is set for detailed error information
        if not success:
            assert hasattr(state_builder, 'last_error'), "Should have last_error attribute"
            if state_builder.last_error:
                assert isinstance(state_builder.last_error, str), "last_error should be string"
                assert len(state_builder.last_error) > 0, "last_error should not be empty"
        
        # Cleanup
        state_builder.cleanup()
    
    def test_resource_cleanup_on_errors(self):
        """Test that resources are properly cleaned up even when errors occur."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        
        state_builder = ScriptBasedStateBuilder()
        
        # Create scenario that will generate a script but might fail execution
        step_table = [{
            'step_no': '1',
            'action': 'navigate',
            'locator_strategy': 'url',
            'locator': 'https://httpbin.org/status/500',  # Server error
            'test_data_param': '',
            'timeout': 10
        }]
        
        success, message, browser_info = state_builder.build_state_with_script(
            step_table=step_table,
            current_step_index=1,
            website_url="https://httpbin.org/status/500",
            test_data={},
            test_case_id="ERROR_CLEANUP_TEST"
        )
        
        # Record script path before cleanup
        script_path = state_builder.partial_script_path
        
        # Cleanup should work regardless of success/failure
        state_builder.cleanup()
        
        # Verify cleanup worked
        if script_path and os.path.exists(script_path):
            # Script might still exist if cleanup_scripts is False
            pass
        else:
            # Script was cleaned up successfully
            assert not os.path.exists(script_path) if script_path else True
    
    def test_concurrent_error_handling(self):
        """Test error handling under concurrent execution scenarios."""
        from core.script_based_state_builder import ScriptBasedStateBuilder
        import concurrent.futures
        
        def execute_with_errors(test_id):
            """Execute state building that might encounter errors."""
            state_builder = ScriptBasedStateBuilder()
            
            # Alternate between valid and invalid scenarios
            if test_id % 2 == 0:
                # Valid scenario
                step_table = [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://httpbin.org/forms/post',
                    'test_data_param': '',
                    'timeout': 15
                }]
                url = "https://httpbin.org/forms/post"
            else:
                # Invalid scenario
                step_table = [{
                    'step_no': '1',
                    'action': 'navigate',
                    'locator_strategy': 'url',
                    'locator': 'https://invalid-domain-concurrent-test.com',
                    'test_data_param': '',
                    'timeout': 5
                }]
                url = "https://invalid-domain-concurrent-test.com"
            
            success, message, _ = state_builder.build_state_with_script(
                step_table=step_table,
                current_step_index=1,
                website_url=url,
                test_data={},
                test_case_id=f"ERROR_CONCURRENT_{test_id:03d}"
            )
            
            state_builder.cleanup()
            return test_id, success, message
        
        # Execute concurrent operations with mixed success/failure scenarios
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(execute_with_errors, i) for i in range(6)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Verify all operations completed (some might fail, but should not crash)
        assert len(results) == 6, "All concurrent operations should complete"
        
        # Check that we have a mix of successes and failures
        successes = sum(1 for _, success, _ in results if success)
        failures = sum(1 for _, success, _ in results if not success)
        
        logger.info(f"Concurrent error handling: {successes} successes, {failures} failures")
        
        # All operations should return valid responses
        for test_id, success, message in results:
            assert isinstance(success, bool), f"Success should be boolean for test {test_id}"
            assert isinstance(message, str), f"Message should be string for test {test_id}"

if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
